# ProxyFlow 监控系统

本文档介绍如何使用 ProxyFlow 的 Prometheus 和 Grafana 监控系统。

## 📊 监控架构

```
ProxyFlow App (:8080/metrics) ──→ Prometheus (:9090) ──→ Grafana (:3000)
```

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

**Linux/macOS:**
```bash
chmod +x scripts/start-monitoring.sh
./scripts/start-monitoring.sh
```

**Windows:**
```cmd
scripts\start-monitoring.bat
```

### 方法二：手动启动

```bash
# 启动监控服务
docker-compose up -d prometheus grafana

# 检查服务状态
docker-compose ps
```

## 🔧 配置文件说明

### Prometheus 配置 (`monitoring/prometheus.yml`)
- **proxy-manager**: 采集主应用指标 (端口 8080)
- 采集间隔: 5-15秒

### Grafana 配置
- **数据源**: `monitoring/grafana/datasources/datasource.yml`
- **仪表板**: `monitoring/grafana/dashboards/`
- **默认登录**: admin/admin

## 📈 监控指标

### ProxyFlow 应用指标

| 指标名称 | 类型 | 描述 |
|---------|------|------|
| `proxy_collector_proxies_collected_total` | Counter | 采集的代理总数 |
| `proxy_collector_proxies_valid_total` | Counter | 有效代理总数 |
| `proxy_collector_proxies_duplicate_total` | Counter | 重复代理总数 |
| `proxy_collector_errors_total` | Counter | 采集错误总数 |
| `proxy_collector_source_errors_total` | Counter | 源错误总数 |
| `proxy_collector_collection_duration_seconds` | Histogram | 采集耗时分布 |

## 🎯 访问地址

| 服务 | 地址 | 用途 |
|------|------|------|
| Prometheus | http://localhost:9090 | 指标查询和告警 |
| Grafana | http://localhost:3000 | 可视化仪表板 |
| ProxyFlow Metrics | http://localhost:8080/metrics | 应用指标端点 |

## 📊 Grafana 仪表板

### 默认仪表板功能
1. **统计面板**: 总采集数、有效代理数、错误数、重复数
2. **趋势图表**: 采集速率、成功率趋势
3. **性能监控**: 采集耗时、响应时间分布

### 自定义仪表板
1. 登录 Grafana (admin/admin)
2. 导航到 "+" → "Dashboard"
3. 添加面板并选择 Prometheus 数据源
4. 使用上述指标创建图表

## 🔍 常用查询

### Prometheus 查询示例

```promql
# 采集速率 (每秒)
rate(proxy_collector_proxies_collected_total[5m])

# 成功率
rate(proxy_collector_proxies_valid_total[5m]) / rate(proxy_collector_proxies_collected_total[5m]) * 100

# 平均采集耗时
rate(proxy_collector_collection_duration_seconds_sum[5m]) / rate(proxy_collector_collection_duration_seconds_count[5m])

```

## 🚨 告警配置

### 创建告警规则
1. 在 `monitoring/prometheus.yml` 中添加 `rule_files`
2. 创建告警规则文件
3. 配置 Alertmanager (可选)

### 示例告警规则
```yaml
groups:
  - name: proxy_manager
    rules:
      - alert: HighCollectionErrors
        expr: rate(proxy_collector_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "代理采集错误率过高"
          
      - alert: LowValidProxyRate
        expr: rate(proxy_collector_proxies_valid_total[5m]) / rate(proxy_collector_proxies_collected_total[5m]) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "有效代理率过低"
```

## 🛠️ 故障排除

### 常见问题

1. **Prometheus 无法采集指标**
   - 检查 ProxyFlow 应用是否运行在 8080 端口
   - 确认 `/metrics` 端点可访问
   - 检查 Docker 网络连接

2. **Grafana 无法连接 Prometheus**
   - 检查 Prometheus 服务状态
   - 确认数据源配置正确
   - 检查网络连接


### 日志查看
```bash
# 查看服务日志
docker-compose logs prometheus
docker-compose logs grafana

# 实时查看日志
docker-compose logs -f prometheus
```

## 🔧 高级配置

### 数据保留
- Prometheus 默认保留 200 小时数据
- 可在 `docker-compose.yml` 中修改 `--storage.tsdb.retention.time`

### 性能优化
- 调整采集间隔以平衡精度和性能
- 配置适当的存储空间
- 使用 Grafana 查询缓存

### 安全配置
- 修改 Grafana 默认密码
- 配置访问控制
- 使用 HTTPS (生产环境)

## 📚 参考资料

- [Prometheus 官方文档](https://prometheus.io/docs/)
- [Grafana 官方文档](https://grafana.com/docs/)
