package handlers

import (
	"crypto/sha256"
	"encoding/hex"
	"net/http"
	"strings"
	"time"

	"proxyFlow/internal/auth"
	"proxyFlow/internal/models"
	"proxyFlow/internal/repository"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// APIKeyHandler API密钥处理器
type APIKeyHandler struct {
	repoManager repository.RepositoryManager
	logger      *logrus.Logger
}

// NewAPIKeyHandler 创建API密钥处理器
func NewAPIKeyHandler(repoManager repository.RepositoryManager, logger *logrus.Logger) *APIKeyHandler {
	return &APIKeyHandler{
		repoManager: repoManager,
		logger:      logger,
	}
}

// GetAPIKeys 获取用户的API密钥列表
func (h *APIKeyHandler) GetAPIKeys(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	userIDStr := userID.(string)

	var apiKeyInfos []*models.APIKeyInfo

	if userIDStr == "super_admin" {
		// 超级管理员返回空的API密钥列表
		apiKeyInfos = []*models.APIKeyInfo{}
	} else {
		apiKeys, err := h.repoManager.GetAPIKey().GetByUserID(ctx, userIDStr)
		if err != nil {
			h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get API keys")
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to get API keys",
				"details": err.Error(),
			})
			return
		}

		// 转换为前端格式
		for _, apiKey := range apiKeys {
			apiKeyInfos = append(apiKeyInfos, apiKey.ToAPIKeyInfo())
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    apiKeyInfos,
	})
}

// CreateAPIKey 创建新的API密钥
func (h *APIKeyHandler) CreateAPIKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userIDStr := userID.(string)

	// 处理超级管理员的特殊情况
	if userIDStr == "super_admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Super admin cannot create API keys"})
		return
	}

	var req models.CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 生成新的API密钥
	apiKeyString, err := auth.GenerateAPIKey()
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate API key"})
		return
	}

	// 计算密钥哈希
	hash := sha256.Sum256([]byte(apiKeyString))
	keyHash := hex.EncodeToString(hash[:])

	// 提取密钥前缀
	keyPrefix := h.extractKeyPrefix(apiKeyString)

	// 调试日志
	h.logger.WithFields(logrus.Fields{
		"api_key_string": apiKeyString,
		"key_prefix":     keyPrefix,
		"prefix_length":  len(keyPrefix),
	}).Info("Generated API key details")

	// 设置默认权限
	if req.Permissions == nil {
		req.Permissions = map[string]interface{}{
			"proxies": []string{"read", "write"},
			"tasks":   []string{"read", "write"},
		}
	}

	// 设置默认速率限制
	rateLimitPerMinute := 1000
	if req.RateLimitPerMinute != nil {
		rateLimitPerMinute = *req.RateLimitPerMinute
	}

	// 计算过期时间
	var expiresAt *time.Time
	if req.ExpiresInDays != nil && *req.ExpiresInDays > 0 {
		expiry := time.Now().AddDate(0, 0, *req.ExpiresInDays)
		expiresAt = &expiry
	}

	// 确保 AllowedIPs 不为 nil
	allowedIPs := req.AllowedIPs
	if allowedIPs == nil {
		allowedIPs = []string{}
	}

	// 简化方案：直接存储完整的API密钥（base64编码）
	// 注意：这种方案安全性较低，仅适用于开发环境或内部系统

	// 创建API密钥记录
	now := time.Now()
	apiKey := &models.APIKey{
		UserID:             userIDStr,
		Name:               req.Name,
		KeyHash:            keyHash,
		KeyPrefix:          keyPrefix,
		FullAPIKey:         &apiKeyString, // 直接存储完整密钥
		Permissions:        req.Permissions,
		IsActive:           true,
		ExpiresAt:          expiresAt,
		UsageCount:         0,
		RateLimitPerMinute: rateLimitPerMinute,
		AllowedIPs:         allowedIPs,
		Metadata:           make(map[string]interface{}),
		CreatedAt:          now,
		UpdatedAt:          now,
	}

	ctx := c.Request.Context()

	h.logger.WithFields(logrus.Fields{
		"user_id":    userIDStr,
		"name":       req.Name,
		"key_prefix": keyPrefix,
	}).Info("Attempting to create API key")

	if err := h.repoManager.GetAPIKey().Create(ctx, apiKey); err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"user_id": userIDStr,
			"name":    req.Name,
		}).Error("Failed to create API key")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create API key",
			"details": err.Error(),
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"user_id":    userIDStr,
		"api_key_id": apiKey.ID,
		"name":       apiKey.Name,
	}).Info("API key created successfully")

	// 返回包含完整密钥的响应
	response := &models.APIKeyResponse{
		APIKeyInfo: *apiKey.ToAPIKeyInfo(),
		APIKey:     apiKeyString,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// UpdateAPIKey 更新API密钥
func (h *APIKeyHandler) UpdateAPIKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	apiKeyID := c.Param("id")
	if apiKeyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "API key ID is required"})
		return
	}

	var req models.UpdateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	ctx := c.Request.Context()

	// 获取现有的API密钥
	apiKey, err := h.repoManager.GetAPIKey().GetByID(ctx, apiKeyID)
	if err != nil {
		h.logger.WithError(err).WithField("api_key_id", apiKeyID).Error("Failed to get API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get API key"})
		return
	}

	if apiKey == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	// 验证用户权限
	if apiKey.UserID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// 更新字段
	if req.Name != nil {
		apiKey.Name = *req.Name
	}
	if req.IsActive != nil {
		apiKey.IsActive = *req.IsActive
	}
	if req.Permissions != nil {
		apiKey.Permissions = req.Permissions
	}
	if req.RateLimitPerMinute != nil {
		apiKey.RateLimitPerMinute = *req.RateLimitPerMinute
	}
	if req.AllowedIPs != nil {
		apiKey.AllowedIPs = req.AllowedIPs
	}

	apiKey.UpdatedAt = time.Now()

	// 保存更新
	if err := h.repoManager.GetAPIKey().Update(ctx, apiKey); err != nil {
		h.logger.WithError(err).WithField("api_key_id", apiKeyID).Error("Failed to update API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update API key"})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"api_key_id": apiKey.ID,
		"name":       apiKey.Name,
	}).Info("API key updated successfully")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    apiKey.ToAPIKeyInfo(),
	})
}

// DeleteAPIKey 删除API密钥
func (h *APIKeyHandler) DeleteAPIKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	apiKeyID := c.Param("id")
	if apiKeyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "API key ID is required"})
		return
	}

	ctx := c.Request.Context()

	// 获取现有的API密钥
	apiKey, err := h.repoManager.GetAPIKey().GetByID(ctx, apiKeyID)
	if err != nil {
		h.logger.WithError(err).WithField("api_key_id", apiKeyID).Error("Failed to get API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get API key"})
		return
	}

	if apiKey == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API key not found"})
		return
	}

	// 验证用户权限
	if apiKey.UserID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// 删除API密钥
	if err := h.repoManager.GetAPIKey().Delete(ctx, apiKeyID); err != nil {
		h.logger.WithError(err).WithField("api_key_id", apiKeyID).Error("Failed to delete API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete API key"})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"api_key_id": apiKey.ID,
		"name":       apiKey.Name,
	}).Info("API key deleted successfully")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "API key deleted successfully",
	})
}

// extractKeyPrefix 提取API密钥前缀
func (h *APIKeyHandler) extractKeyPrefix(apiKey string) string {
	parts := strings.Split(apiKey, "_")
	if len(parts) >= 3 {
		prefix := strings.Join(parts[:2], "_") + "_" + parts[2][:4]
		// 确保不超过20个字符
		if len(prefix) > 20 {
			return prefix[:20]
		}
		return prefix
	}
	// 对于没有下划线的密钥，取前10个字符
	if len(apiKey) > 10 {
		return apiKey[:10]
	}
	return apiKey
}
