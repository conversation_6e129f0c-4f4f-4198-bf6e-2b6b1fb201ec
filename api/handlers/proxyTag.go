package handlers

import (
	"net/http"

	"proxyFlow/internal/models"
	"proxyFlow/internal/proxy"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// ProxyTagHandler 代理标签处理器
type ProxyTagHandler struct {
	proxyManager *proxy.Manager
	logger       *logrus.Logger
}

// NewProxyTagHandler 创建代理标签处理器
func NewProxyTagHandler(proxyManager *proxy.Manager, logger *logrus.Logger) *ProxyTagHandler {
	return &ProxyTagHandler{
		proxyManager: proxyManager,
		logger:       logger,
	}
}

// CreateTag 创建标签
func (h *ProxyTagHandler) CreateTag(c *gin.Context) {
	var req models.ProxyTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	tag, err := h.proxyManager.CreateProxyTag(ctx, &req, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to create proxy tag")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create tag"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Tag created successfully",
		"data":    tag,
	})
}

// GetTags 获取所有标签
func (h *ProxyTagHandler) GetTags(c *gin.Context) {
	ctx := c.Request.Context()
	tags, err := h.proxyManager.GetProxyTags(ctx)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get proxy tags")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tags"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  tags,
		"total": len(tags),
	})
}

// GetTag 获取单个标签
func (h *ProxyTagHandler) GetTag(c *gin.Context) {
	tagID := c.Param("id")
	if tagID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tag ID is required"})
		return
	}

	ctx := c.Request.Context()
	tag, err := h.proxyManager.GetProxyTag(ctx, tagID)
	if err != nil {
		h.logger.WithError(err).WithField("tag_id", tagID).Error("Failed to get proxy tag")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tag"})
		return
	}

	if tag == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Tag not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": tag})
}

// UpdateTag 更新标签
func (h *ProxyTagHandler) UpdateTag(c *gin.Context) {
	tagID := c.Param("id")
	if tagID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tag ID is required"})
		return
	}

	var req models.ProxyTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx := c.Request.Context()
	tag, err := h.proxyManager.UpdateProxyTag(ctx, tagID, &req)
	if err != nil {
		h.logger.WithError(err).WithField("tag_id", tagID).Error("Failed to update proxy tag")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update tag"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Tag updated successfully",
		"data":    tag,
	})
}

// DeleteTag 删除标签
func (h *ProxyTagHandler) DeleteTag(c *gin.Context) {
	tagID := c.Param("id")
	if tagID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tag ID is required"})
		return
	}

	ctx := c.Request.Context()
	err := h.proxyManager.DeleteProxyTag(ctx, tagID)
	if err != nil {
		h.logger.WithError(err).WithField("tag_id", tagID).Error("Failed to delete proxy tag")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete tag"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Tag deleted successfully"})
}

// AssignTags 为代理分配标签
func (h *ProxyTagHandler) AssignTags(c *gin.Context) {
	var req models.ProxyTagAssignRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()

	// 为每个代理分配标签
	var errors []string
	successCount := 0

	for _, proxyID := range req.ProxyIDs {
		err := h.proxyManager.AssignTagsToProxy(ctx, proxyID, req.TagIDs, userID.(string))
		if err != nil {
			h.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to assign tags to proxy")
			errors = append(errors, err.Error())
		} else {
			successCount++
		}
	}

	response := gin.H{
		"message": "Tag assignment completed",
		"success": successCount,
		"total":   len(req.ProxyIDs),
	}

	if len(errors) > 0 {
		response["errors"] = errors
	}

	c.JSON(http.StatusOK, response)
}

// RemoveTags 移除代理的标签
func (h *ProxyTagHandler) RemoveTags(c *gin.Context) {
	proxyID := c.Param("proxy_id")
	if proxyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Proxy ID is required"})
		return
	}

	var req struct {
		TagIDs []string `json:"tag_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx := c.Request.Context()
	err := h.proxyManager.RemoveTagsFromProxy(ctx, proxyID, req.TagIDs)
	if err != nil {
		h.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to remove tags from proxy")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove tags"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Tags removed successfully"})
}

// GetProxyTags 获取代理的标签
func (h *ProxyTagHandler) GetProxyTags(c *gin.Context) {
	proxyID := c.Param("proxy_id")
	if proxyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Proxy ID is required"})
		return
	}

	ctx := c.Request.Context()
	tags, err := h.proxyManager.GetProxyTagsByProxyID(ctx, proxyID)
	if err != nil {
		h.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to get proxy tags")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxy tags"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  tags,
		"total": len(tags),
	})
}
