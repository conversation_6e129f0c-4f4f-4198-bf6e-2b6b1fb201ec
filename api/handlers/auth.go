package handlers

import (
	"net/http"
	"time"

	"proxyFlow/internal/auth"
	"proxyFlow/internal/models"
	"proxyFlow/internal/repository"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	repoManager repository.RepositoryManager
	jwtManager  *auth.JWTManager
	logger      *logrus.Logger
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(repoManager repository.RepositoryManager, jwtManager *auth.JWTManager, logger *logrus.Logger) *AuthHandler {
	return &AuthHandler{
		repoManager: repoManager,
		jwtManager:  jwtManager,
		logger:      logger,
	}
}

// Register 用户注册
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.UserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx := c.Request.Context()

	// 检查用户名是否已存在
	existingUser, _ := h.repoManager.GetUser().GetByUsername(ctx, req.Username)
	if existingUser != nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Username already exists"})
		return
	}

	// 检查邮箱是否已存在
	existingUser, _ = h.repoManager.GetUser().GetByEmail(ctx, req.Email)
	if existingUser != nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Email already exists"})
		return
	}

	// 哈希密码
	hashedPassword, err := auth.HashPassword(req.Password)
	if err != nil {
		h.logger.WithError(err).Error("Failed to hash password")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// 创建用户（不再使用旧版本API密钥系统）
	now := time.Now()
	user := &models.User{
		ID:        "", // 让数据库自动生成 UUID
		Username:  req.Username,
		Email:     req.Email,
		Password:  hashedPassword,
		Role:      req.Role,
		Status:    models.UserStatusActive,
		CreatedAt: now,
		UpdatedAt: now,
		// 不再设置旧版本的API密钥字段
		APIKey:           "",
		APIKeyCreated:    time.Time{},
		APIKeyLastUsed:   time.Time{},
		APIKeyUsageCount: 0,
	}

	// 保存用户到PostgreSQL
	if err := h.repoManager.GetUser().Create(ctx, user); err != nil {
		h.logger.WithError(err).Error("Failed to save user")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "User registered successfully",
		"data": models.UserResponse{
			ID:        user.ID,
			Username:  user.Username,
			Email:     user.Email,
			Role:      user.Role,
			Status:    user.Status,
			CreatedAt: user.CreatedAt,
		},
	})
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx := c.Request.Context()

	// 获取用户
	user, err := h.repoManager.GetUser().GetByUsername(ctx, req.Username)
	if err != nil || user == nil {
		h.logger.WithFields(logrus.Fields{
			"username": req.Username,
		}).Warn("User not found during login")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// 检查密码
	if !auth.CheckPassword(req.Password, user.Password) {
		h.logger.WithFields(logrus.Fields{
			"username": req.Username,
		}).Warn("Password check failed during login")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// 检查用户状态
	if user.Status != models.UserStatusActive {
		c.JSON(http.StatusForbidden, gin.H{"error": "Account is not active"})
		return
	}

	// 生成token
	token, err := h.jwtManager.GenerateToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate token")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// 更新最后登录时间
	now := time.Now()
	if err := h.repoManager.GetUser().UpdateLastLogin(ctx, user.ID, now); err != nil {
		h.logger.WithError(err).Error("Failed to update last login time")
		// 不阻断登录流程，只记录错误
	}
	user.LastLogin = &now

	c.JSON(http.StatusOK, gin.H{
		"message": "Login successful",
		"data": models.LoginResponse{
			AccessToken: token,
			ExpiresIn:   int64(h.jwtManager.TokenDuration().Seconds()),
			User: models.UserResponse{
				ID:        user.ID,
				Username:  user.Username,
				Email:     user.Email,
				Role:      user.Role,
				Status:    user.Status,
				CreatedAt: user.CreatedAt,
				LastLogin: user.LastLogin,
			},
		},
	})
}

// GetProfile 获取用户资料
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	user, err := h.repoManager.GetUser().GetByID(ctx, userID.(string))
	if err != nil || user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": models.UserResponse{
			ID:        user.ID,
			Username:  user.Username,
			Email:     user.Email,
			Role:      user.Role,
			Status:    user.Status,
			CreatedAt: user.CreatedAt,
			LastLogin: user.LastLogin,
		},
	})
}

// maskAPIKey 隐藏 API Key 的中间部分
func (h *AuthHandler) maskAPIKey(apiKey string) string {
	if len(apiKey) <= 8 {
		return apiKey
	}
	return apiKey[:4] + "****" + apiKey[len(apiKey)-4:]
}

// GetAPIKey 获取当前用户的 API Key 信息
func (h *AuthHandler) GetAPIKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	user, err := h.repoManager.GetUser().GetByID(ctx, userID.(string))
	if err != nil || user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	response := models.APIKeyStatsResponse{
		APIKeyCreated:    user.APIKeyCreated,
		APIKeyLastUsed:   user.APIKeyLastUsed,
		APIKeyUsageCount: user.APIKeyUsageCount,
		MaskedAPIKey:     h.maskAPIKey(user.APIKey),
	}

	c.JSON(http.StatusOK, gin.H{
		"data": response,
	})
}
