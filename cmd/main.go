package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"syscall"
	"time"

	"proxyFlow/internal/app"
	"proxyFlow/pkg/config"

	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
)

func main() {
	// 1. 初始化.env
	if err := initializeEnvironment(); err != nil {
		log.Fatalf("Failed to initialize environment: %v", err)
	}

	// 2. 加载config.yaml配置
	cfg, err := loadConfiguration()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// 3. 设置日志
	logger := setupLogger(cfg.Logging)
	logger.Info("Starting Proxy Manager...")

	// 4. 创建应用实例
	application, err := app.NewApplication(cfg, logger)
	if err != nil {
		logger.Fatalf("Failed to create application: %v", err)
	}

	// 5. 启动应用
	ctx := context.Background()
	if err := application.Start(ctx); err != nil {
		logger.Fatalf("Failed to start application: %v", err)
	}

	// 6. 优雅关闭
	gracefulShutdown(application, logger)
}

func initializeEnvironment() error {
	currentDir := getCurrentFileDir()
	envPath := filepath.Join(currentDir, "..", ".env")
	return godotenv.Load(envPath)
}

func loadConfiguration() (*config.Config, error) {
	currentDir := getCurrentFileDir()
	configPath := filepath.Join(currentDir, "..", "config", "config.yaml")

	cfg, err := config.Load(configPath)
	if err != nil {
		cfg = config.GetDefaultConfig()
		log.Printf("Using default config: %v", err)
	}
	return cfg, nil
}

func gracefulShutdown(app *app.Application, logger *logrus.Logger) {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := app.Stop(shutdownCtx); err != nil {
		logger.Errorf("Failed to shutdown gracefully: %v", err)
	}

	logger.Info("Server exited")
}

func getCurrentFileDir() string {
	_, file, _, ok := runtime.Caller(1)
	if !ok {
		panic("unable to get current file path")
	}
	return filepath.Dir(file)
}

func setupLogger(cfg config.LoggingConfig) *logrus.Logger {
	logger := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	if cfg.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}

	// 设置日志输出
	if cfg.Output == "file" {
		file, err := os.OpenFile(cfg.FilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			log.Printf("Failed to open log file: %v", err)
		} else {
			logger.SetOutput(file)
		}
	}

	return logger
}
