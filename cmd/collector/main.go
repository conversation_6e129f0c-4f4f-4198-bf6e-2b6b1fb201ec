package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"syscall"

	"proxyFlow/internal/collector"
	"proxyFlow/internal/repository/postgres"
	"proxyFlow/pkg/config"
	"proxyFlow/pkg/database"

	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
)

// initializeEnvironment 初始化环境
func initializeEnvironment() error {
	currentDir := getCurrentFileDir()
	envPath := filepath.Join(currentDir, "..", "..", ".env")
	return godotenv.Load(envPath)
}
func main() {
	// 命令行参数
	var (
		configPath  = flag.String("config", "config/config.yaml", "配置文件路径")
		immediate   = flag.Bool("immediate", false, "立即执行一次采集")
		daemon      = flag.Bool("daemon", false, "作为守护进程运行")
		force       = flag.Bool("force", false, "强制重新全量运行（忽略缓存和进度）")
		logLevel    = flag.String("log-level", "info", "日志级别 (debug, info, warn, error)")
		showVersion = flag.Bool("version", false, "显示版本信息")
	)
	flag.Parse()

	// 初始化环境
	if err := initializeEnvironment(); err != nil {
		log.Fatalf("Failed to initialize environment: %v", err)
	}

	if *showVersion {
		fmt.Println("ProxyManager Collector v1.0.0")
		os.Exit(0)
	}

	// 创建日志器
	logger := logrus.New()
	level, err := logrus.ParseLevel(*logLevel)
	if err != nil {
		logger.WithError(err).Fatal("Invalid log level")
	}
	logger.SetLevel(level)
	logger.SetFormatter(&logrus.JSONFormatter{})

	logger.WithFields(logrus.Fields{
		"config_path": *configPath,
		"immediate":   *immediate,
		"daemon":      *daemon,
		"force":       *force,
		"log_level":   *logLevel,
	}).Info("Starting ProxyManager Collector")

	// 加载配置
	cfg, err := loadConfiguration(*configPath)
	if err != nil {
		logger.WithError(err).Fatal("Failed to load configuration")
	}

	// 初始化数据库连接
	postgresClient, err := database.NewPostgresClient(&database.PostgresConfig{
		Host:            cfg.Postgres.Host,
		Port:            cfg.Postgres.Port,
		User:            cfg.Postgres.User,
		Password:        cfg.Postgres.Password,
		Database:        cfg.Postgres.Database,
		SSLMode:         cfg.Postgres.SSLMode,
		MaxOpenConns:    cfg.Postgres.MaxOpenConns,
		MaxIdleConns:    cfg.Postgres.MaxIdleConns,
		ConnMaxLifetime: cfg.Postgres.ConnMaxLifetime,
		ConnMaxIdleTime: cfg.Postgres.ConnMaxIdleTime,
	}, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to PostgreSQL")
	}
	defer postgresClient.Close()

	// 创建仓储管理器
	repoManager := postgres.NewRepositoryManager(postgresClient, logger)

	// 创建上下文
	ctx := context.Background()

	// 创建代理采集器
	proxyCollector := collector.NewProxyCollector(
		&cfg.Collector,
		repoManager.GetProxy(),
		logger,
	)

	// 处理force选项
	if *force {
		logger.Info("Force mode enabled - clearing all caches and progress")
		if err := proxyCollector.ForceReset(); err != nil {
			logger.WithError(err).Fatal("Failed to perform force reset")
		}
		logger.Info("Force reset completed, starting fresh collection")
	}

	// 根据模式运行
	if *immediate {
		// 立即执行一次采集
		logger.Info("Running immediate collection")
		stats, err := proxyCollector.CollectOnce(ctx)
		if err != nil {
			logger.WithError(err).Error("Collection failed")
			os.Exit(1)
		}

		logger.WithFields(logrus.Fields{
			"total_proxies":   stats.TotalProxies,
			"valid_proxies":   stats.ValidProxies,
			"duplicate_count": stats.DuplicateCount,
			"duration":        stats.Duration,
		}).Info("Collection completed successfully")

	} else if *daemon {
		// 作为守护进程运行
		logger.Info("Starting collector daemon")

		// 启动采集器
		if err := proxyCollector.Start(ctx); err != nil {
			logger.WithError(err).Fatal("Failed to start collector")
		}

		// 等待中断信号
		signalChan := make(chan os.Signal, 1)
		signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

		logger.Info("Collector daemon started, waiting for signals...")
		<-signalChan

		logger.Info("Shutting down collector daemon")
		if err := proxyCollector.Stop(); err != nil {
			logger.WithError(err).Error("Failed to stop collector gracefully")
		}

	} else {
		// 默认：启动一次性任务
		logger.Info("Starting one-time collection task")

		// 创建可控的采集任务
		taskConfig := map[string]interface{}{
			"immediate": true,
			"priority":  collector.PriorityHigh,
			"source":    "standalone_command",
			"force":     *force,
		}

		task, err := proxyCollector.StartTask(ctx, taskConfig)
		if err != nil {
			logger.WithError(err).Fatal("Failed to start collection task")
		}

		logger.WithField("task_id", task.ID).Info("Collection task started")

		// 监控任务进度
		progressCh, err := proxyCollector.SubscribeProgress(task.ID)
		if err != nil {
			logger.WithError(err).Warn("Failed to subscribe to progress")
		}

		// 启动采集器服务
		if err := proxyCollector.Start(ctx); err != nil {
			logger.WithError(err).Fatal("Failed to start collector service")
		}

		// 监控进度直到完成
		for {
			select {
			case progress := <-progressCh:
				logger.WithFields(logrus.Fields{
					"task_id":   task.ID,
					"phase":     progress.Phase,
					"progress":  fmt.Sprintf("%.2f%%", progress.ProgressPercent),
					"processed": progress.ProcessedCount,
					"total":     progress.TotalCount,
					"speed":     fmt.Sprintf("%.2f/s", progress.Speed),
					"eta":       progress.ETA,
					"status":    progress.Status,
				}).Info("Collection progress")

				// 检查任务是否完成
				if progress.Status == "completed" || progress.Status == "error" || progress.Status == "cancelled" {
					logger.WithFields(logrus.Fields{
						"task_id": task.ID,
						"status":  progress.Status,
					}).Info("Collection task finished")

					// 获取最终统计
					finalTask, err := proxyCollector.GetTask(task.ID)
					if err == nil && finalTask.Stats != nil {
						logger.WithFields(logrus.Fields{
							"total_proxies":   finalTask.Stats.TotalProxies,
							"valid_proxies":   finalTask.Stats.ValidProxies,
							"duplicate_count": finalTask.Stats.DuplicateCount,
							"duration":        finalTask.Stats.Duration,
							"success_rate":    finalTask.Stats.SuccessRate,
						}).Info("Final collection statistics")
					}

					goto cleanup
				}

			case <-ctx.Done():
				logger.Info("Context cancelled, stopping collector")
				goto cleanup
			}
		}

	cleanup:
		// 停止采集器
		if err := proxyCollector.Stop(); err != nil {
			logger.WithError(err).Error("Failed to stop collector")
		}
	}

	logger.Info("ProxyManager Collector finished")
}

// loadConfiguration 加载配置
func loadConfiguration(configPath string) (*config.Config, error) {
	// 如果是相对路径，转换为绝对路径
	if !filepath.IsAbs(configPath) {
		currentDir := getCurrentFileDir()
		configPath = filepath.Join(currentDir, "..", "..", configPath)
	}

	cfg, err := config.Load(configPath)
	if err != nil {
		cfg = config.GetDefaultConfig()
	}
	return cfg, nil
}

// getCurrentFileDir 获取当前文件目录
func getCurrentFileDir() string {
	_, file, _, ok := runtime.Caller(1)
	if !ok {
		panic("unable to get current file path")
	}
	return filepath.Dir(file)
}
