# ProxyFlow API 文档

本文档详细介绍了 ProxyFlow 高性能代理管理系统的 API 接口。

**基础 URL**: `/api/v1`
**API 文档**: http://localhost:8080/api/v1/docs
**健康检查**: http://localhost:8080/health
**监控指标**: http://localhost:8080/metrics

## 认证方式

ProxyFlow 支持两种认证方式：

### 1. JWT Token 认证
在请求头中加入 `Authorization: Bearer <your_jwt_token>`

### 2. API Key 认证
在请求头中加入 `Authorization: Bearer <your_api_key>`

---

## 🔐 认证管理 (Authentication)

### 1. 用户注册

*   **POST** `/auth/register`
*   **描述**: 创建一个新用户账户
*   **认证**: 无需
*   **请求体**:
    ```json
    {
      "username": "testuser",
      "email": "<EMAIL>",
      "password": "yoursecurepassword",
      "role": "user"
    }
    ```
*   **响应 (201 Created)**:
    ```json
    {
      "message": "User registered successfully",
      "data": {
        "id": "user-uuid",
        "username": "testuser",
        "email": "<EMAIL>",
        "role": "user",
        "status": "active",
        "created_at": "2024-01-15T10:00:00Z"
      }
    }
    ```

### 2. 用户登录

*   **POST** `/auth/login`
*   **描述**: 用户登录获取 JWT Token
*   **认证**: 无需
*   **请求体**:
    ```json
    {
      "username": "testuser",
      "password": "yoursecurepassword"
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Login successful",
      "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 86400,
        "user": {
          "id": "user-uuid",
          "username": "testuser",
          "email": "<EMAIL>",
          "role": "user",
          "status": "active",
          "created_at": "2024-01-15T10:00:00Z",
          "last_login": "2024-01-15T10:30:00Z"
        }
      }
    }
    ```

### 3. 获取用户资料

*   **GET** `/auth/profile`
*   **描述**: 获取当前登录用户的个人资料
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "id": "user-uuid",
        "username": "testuser",
        "email": "<EMAIL>",
        "role": "user",
        "status": "active",
        "created_at": "2024-01-15T10:00:00Z",
        "last_login": "2024-01-15T10:30:00Z"
      }
    }
    ```

---

## 🔑 API 密钥管理 (API Key Management)

### 1. 获取 API 密钥列表

*   **GET** `/user/apikeys`
*   **描述**: 获取当前用户的所有 API 密钥
*   **认证**: 需要 JWT Token
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": "key-uuid",
          "name": "My Production Key",
          "key_prefix": "pm_prod_****",
          "masked_api_key": "pm_prod_**********************",
          "permissions": {
            "proxies": ["read", "write"],
            "tasks": ["read", "write"]
          },
          "is_active": true,
          "expires_at": "2024-12-31T23:59:59Z",
          "last_used_at": "2024-01-15T10:30:00Z",
          "usage_count": 1250,
          "rate_limit_per_minute": 100,
          "created_at": "2024-01-01T00:00:00Z"
        }
      ]
    }
    ```

### 2. 创建 API 密钥

*   **POST** `/user/apikeys`
*   **描述**: 创建一个新的 API 密钥
*   **认证**: 需要 JWT Token
*   **请求体**:
    ```json
    {
      "name": "My New API Key",
      "expires_in_days": 365,
      "permissions": {
        "proxies": ["read", "write"],
        "tasks": ["read"]
      },
      "rate_limit_per_minute": 60,
      "allowed_ips": ["*************", "10.0.0.0/8"]
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "id": "new-key-uuid",
        "name": "My New API Key",
        "api_key": "pm_live_abcdef1234567890abcdef1234567890",
        "key_prefix": "pm_live_****",
        "permissions": {
          "proxies": ["read", "write"],
          "tasks": ["read"]
        },
        "expires_at": "2025-01-15T10:30:00Z",
        "rate_limit_per_minute": 60,
        "created_at": "2024-01-15T10:30:00Z"
      }
    }
    ```

### 3. 更新 API 密钥

*   **PUT** `/user/apikeys/:id`
*   **描述**: 更新已存在的 API 密钥设置
*   **认证**: 需要 JWT Token
*   **请求体**:
    ```json
    {
      "name": "Updated Key Name",
      "is_active": false,
      "rate_limit_per_minute": 30
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "id": "key-uuid",
        "name": "Updated Key Name",
        "is_active": false,
        "rate_limit_per_minute": 30,
        "updated_at": "2024-01-15T10:30:00Z"
      }
    }
    ```

### 4. 删除 API 密钥

*   **DELETE** `/user/apikeys/:id`
*   **描述**: 删除指定的 API 密钥
*   **认证**: 需要 JWT Token
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "message": "API key deleted successfully"
    }
    ```

---

## 🌐 代理管理 (Proxy Management)

### 1. 获取代理列表

*   **GET** `/proxies`
*   **描述**: 获取代理列表，支持分页、筛选和排序
*   **认证**: 需要
*   **查询参数**:
    - `page`: 页码 (默认: 1)
    - `limit`: 每页数量 (默认: 20, 最大: 100)
    - `status`: 代理状态筛选 (`active`, `inactive`, `failed`)
    - `type`: 代理类型筛选 (`http`, `https`, `socks5`)
    - `country_code`: 国家代码筛选 (如: `US`, `CN`)
    - `search`: 搜索关键词 (主机地址或标签)
    - `sort`: 排序字段 (`created_at`, `last_check`, `response_time`)
    - `order`: 排序方向 (`asc`, `desc`)
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "proxy-uuid",
          "host": "*************",
          "port": 8080,
          "type": "http",
          "status": "active",
          "username": "user123",
          "failures": 0,
          "last_check": "2024-01-15T10:30:00Z",
          "response_time": 120,
          "country_code": "US",
          "city_name": "New York",
          "asn_name": "Cloudflare Inc",
          "anonymity_level": "elite",
          "quality_score": 0.95,
          "reliability_score": 0.88,
          "tags": [
            {
              "id": "tag-uuid",
              "name": "High-Speed",
              "color": "#00FF00"
            }
          ],
          "scenario": "web_scraping",
          "priority": 1,
          "created_at": "2024-01-15T10:00:00Z"
        }
      ],
      "pagination": {
        "page": 1,
        "limit": 20,
        "total": 150,
        "pages": 8
      }
    }
    ```

### 2. 获取单个代理详情

*   **GET** `/proxies/:id`
*   **描述**: 获取指定代理的详细信息
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "id": "proxy-uuid",
        "host": "*************",
        "port": 8080,
        "type": "http",
        "status": "active",
        "username": "user123",
        "failures": 0,
        "last_check": "2024-01-15T10:30:00Z",
        "response_time": 120,
        "country_code": "US",
        "city_name": "New York",
        "asn_name": "Cloudflare Inc",
        "asn_number": 13335,
        "anonymity_level": "elite",
        "quality_score": 0.95,
        "reliability_score": 0.88,
        "last_quality_check": "2024-01-15T10:25:00Z",
        "tags": [],
        "scenario": "web_scraping",
        "priority": 1,
        "created_at": "2024-01-15T10:00:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    }
    ```

### 3. 添加代理

*   **POST** `/proxies`
*   **描述**: 添加一个新的代理
*   **认证**: 需要，**管理员权限**
*   **请求体**:
    ```json
    {
      "host": "*************",
      "port": 8080,
      "type": "http",
      "username": "user123",
      "password": "pass123",
      "country_code": "US",
      "city_name": "Los Angeles",
      "scenario": "web_scraping",
      "priority": 1,
      "tag_ids": ["tag-uuid-1", "tag-uuid-2"]
    }
    ```
*   **响应 (201 Created)**:
    ```json
    {
      "message": "Proxy added successfully",
      "data": {
        "id": "new-proxy-uuid",
        "host": "*************",
        "port": 8080,
        "type": "http",
        "status": "active",
        "created_at": "2024-01-15T10:30:00Z"
      }
    }
    ```

### 4. 删除代理

*   **DELETE** `/proxies/:id`
*   **描述**: 删除指定的代理
*   **认证**: 需要，**管理员权限**
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Proxy deleted successfully"
    }
    ```

### 5. 批量导入代理

*   **POST** `/proxies/batch-import`
*   **描述**: 批量导入多个代理
*   **认证**: 需要，**管理员权限**
*   **请求体**:
    ```json
    [
      {
        "host": "*************",
        "port": 8080,
        "type": "http",
        "username": "user1",
        "password": "pass1",
        "country_code": "US"
      },
      {
        "host": "*************",
        "port": 1080,
        "type": "socks5",
        "country_code": "JP"
      }
    ]
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Batch import completed",
      "success": 2,
      "failed": 0,
      "errors": []
    }
    ```

### 6. 获取可用代理

*   **GET** `/proxies/available`
*   **描述**: 获取当前可用的代理列表
*   **认证**: 需要
*   **查询参数**:
    - `limit`: 返回数量限制 (默认: 10)
    - `country_code`: 国家代码筛选
    - `type`: 代理类型筛选
    - `min_quality`: 最低质量分数 (0.0-1.0)
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "proxy-uuid",
          "host": "*************",
          "port": 8080,
          "type": "http",
          "status": "active",
          "response_time": 120,
          "quality_score": 0.95,
          "country_code": "US"
        }
      ],
      "total": 25
    }
    ```

### 7. 代理健康检查

*   **POST** `/proxies/:id/health-check`
*   **描述**: 对单个代理进行健康检查
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "proxy_id": "proxy-uuid",
        "status": "active",
        "response_time": 120,
        "anonymity_level": "elite",
        "checked_at": "2024-01-15T10:30:00Z",
        "success": true
      }
    }
    ```

### 8. 批量健康检查

*   **POST** `/proxies/batch/health-check`
*   **描述**: 对多个代理进行批量健康检查
*   **认证**: 需要
*   **请求体**:
    ```json
    {
      "proxy_ids": ["proxy-uuid-1", "proxy-uuid-2", "proxy-uuid-3"]
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "proxy_id": "proxy-uuid-1",
          "status": "active",
          "response_time": 120,
          "success": true
        },
        {
          "proxy_id": "proxy-uuid-2",
          "status": "failed",
          "error": "Connection timeout",
          "success": false
        }
      ],
      "total": 2,
      "success_count": 1,
      "failed_count": 1
    }
    ```

### 9. 全量健康检查

*   **POST** `/proxies/health-check-all`
*   **描述**: 对所有代理进行健康检查
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Health check started for all proxies",
      "total_proxies": 150,
      "estimated_duration": "5 minutes"
    }
    ```

## 🌍 地理位置和高级筛选

### 1. 按地理位置获取代理

*   **GET** `/proxies/location`
*   **描述**: 根据地理位置获取代理
*   **认证**: 需要
*   **查询参数**:
    - `country_code`: 国家代码 (必需)
    - `city_name`: 城市名称 (可选)
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "proxy-uuid",
          "host": "*************",
          "port": 8080,
          "type": "http",
          "country_code": "US",
          "city_name": "New York",
          "asn_name": "Cloudflare Inc",
          "quality_score": 0.95
        }
      ],
      "total": 15,
      "filters": {
        "country_code": "US",
        "city_name": "New York"
      }
    }
    ```

### 2. 获取代理地理位置统计

*   **GET** `/proxies/location/stats`
*   **描述**: 获取代理按地理位置的统计信息
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "total_countries": 25,
        "total_cities": 150,
        "by_country": [
          {
            "country_code": "US",
            "country_name": "United States",
            "proxy_count": 45,
            "avg_quality": 0.85
          },
          {
            "country_code": "JP",
            "country_name": "Japan",
            "proxy_count": 30,
            "avg_quality": 0.92
          }
        ]
      }
    }
    ```

### 3. 按地理位置分组代理

*   **GET** `/proxies/location/groups`
*   **描述**: 按地理位置对代理进行分组
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "US": {
          "country_name": "United States",
          "cities": {
            "New York": {
              "proxy_count": 15,
              "avg_response_time": 120
            },
            "Los Angeles": {
              "proxy_count": 20,
              "avg_response_time": 95
            }
          }
        },
        "JP": {
          "country_name": "Japan",
          "cities": {
            "Tokyo": {
              "proxy_count": 25,
              "avg_response_time": 80
            }
          }
        }
      }
    }
    ```

## ⭐ 质量评估和智能路由

### 1. 按质量获取代理

*   **GET** `/proxies/quality`
*   **描述**: 根据质量分数范围获取代理
*   **认证**: 需要
*   **查询参数**:
    - `min_score`: 最低质量分数 (0.0-1.0)
    - `max_score`: 最高质量分数 (0.0-1.0)
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "proxy-uuid",
          "host": "*************",
          "port": 8080,
          "quality_score": 0.95,
          "reliability_score": 0.88,
          "response_time": 120,
          "anonymity_level": "elite"
        }
      ],
      "total": 25,
      "filters": {
        "min_score": 0.8,
        "max_score": 1.0
      }
    }
    ```

### 2. 获取顶级质量代理

*   **GET** `/proxies/quality/top`
*   **描述**: 获取质量最高的代理列表
*   **认证**: 需要
*   **查询参数**:
    - `limit`: 返回数量 (默认: 10)
    - `type`: 代理类型筛选
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "proxy-uuid",
          "host": "*************",
          "port": 8080,
          "type": "http",
          "quality_score": 0.98,
          "response_time": 85,
          "anonymity_level": "elite",
          "country_code": "JP"
        }
      ],
      "total": 10
    }
    ```

### 3. 批量质量评估

*   **POST** `/proxies/quality/assess`
*   **描述**: 对指定代理进行批量质量评估
*   **认证**: 需要，**管理员权限**
*   **请求体**:
    ```json
    {
      "proxy_ids": ["proxy-uuid-1", "proxy-uuid-2"]
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Quality assessment started",
      "total_proxies": 2,
      "estimated_duration": "30 seconds"
    }
    ```

### 4. 智能路由获取代理

*   **GET** `/proxies/smart-routing`
*   **描述**: 使用智能路由算法获取最佳代理
*   **认证**: 需要
*   **查询参数**:
    - `tags`: 标签筛选 (多个用逗号分隔)
    - `scenario`: 使用场景 (`web_scraping`, `api_testing`, `social_media`)
    - `strategy`: 选择策略 (`best_quality`, `fastest`, `most_reliable`)
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "id": "proxy-uuid",
        "host": "*************",
        "port": 8080,
        "type": "http",
        "quality_score": 0.95,
        "response_time": 85,
        "selected_reason": "Best quality match for web_scraping scenario"
      }
    }
    ```

### 5. 按标签获取代理

*   **GET** `/proxies/tags`
*   **描述**: 根据标签获取代理列表
*   **认证**: 需要
*   **查询参数**:
    - `tags`: 标签名称数组 (必需)
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "proxy-uuid",
          "host": "*************",
          "port": 8080,
          "type": "http",
          "tags": [
            {
              "id": "tag-uuid",
              "name": "High-Speed",
              "color": "#00FF00"
            }
          ]
        }
      ],
      "total": 15,
      "tags": ["High-Speed", "Reliable"]
    }
    ```

### 6. 按场景获取代理

*   **GET** `/proxies/scenario`
*   **描述**: 根据使用场景获取代理
*   **认证**: 需要
*   **查询参数**:
    - `scenario`: 场景名称 (必需)
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "proxy-uuid",
          "host": "*************",
          "port": 8080,
          "type": "http",
          "scenario": "web_scraping",
          "priority": 1
        }
      ],
      "total": 20,
      "scenario": "web_scraping"
    }
    ```

### 7. 按策略获取代理

*   **GET** `/proxies/strategy`
*   **描述**: 使用指定策略获取代理
*   **认证**: 需要
*   **查询参数**:
    - `strategy`: 策略名称 (`round_robin`, `least_used`, `random`, `weighted`)
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "id": "proxy-uuid",
        "host": "*************",
        "port": 8080,
        "type": "http",
        "strategy_used": "round_robin",
        "last_used": "2024-01-15T10:25:00Z"
      }
    }
    ```

### 8. 获取代理统计

*   **GET** `/proxies/stats`
*   **描述**: 获取代理池的统计信息
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "total_proxies": 150,
        "active_proxies": 120,
        "inactive_proxies": 25,
        "failed_proxies": 5,
        "by_type": {
          "http": 80,
          "https": 45,
          "socks5": 25
        },
        "by_country": {
          "US": 45,
          "JP": 30,
          "DE": 25,
          "others": 50
        },
        "avg_quality_score": 0.78,
        "avg_response_time": 145
      }
    }
    ```

---

## 🏷️ 标签管理 (Tag Management)

### 1. 获取所有标签

*   **GET** `/tags`
*   **描述**: 获取所有代理标签列表
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "tag-uuid",
          "name": "High-Speed",
          "description": "高速代理标签",
          "color": "#00FF00",
          "created_at": "2024-01-15T10:00:00Z",
          "updated_at": "2024-01-15T10:00:00Z",
          "created_by": "admin"
        }
      ],
      "total": 1
    }
    ```

### 2. 创建标签

*   **POST** `/tags`
*   **描述**: 创建一个新的代理标签
*   **认证**: 需要，**管理员权限**
*   **请求体**:
    ```json
    {
      "name": "High-Speed",
      "description": "高速代理标签",
      "color": "#00FF00"
    }
    ```
*   **响应 (201 Created)**:
    ```json
    {
      "message": "Tag created successfully",
      "data": {
        "id": "new-tag-uuid",
        "name": "High-Speed",
        "description": "高速代理标签",
        "color": "#00FF00",
        "created_at": "2024-01-15T10:30:00Z"
      }
    }
    ```

### 3. 更新标签

*   **PUT** `/tags/:id`
*   **描述**: 更新指定标签的信息
*   **认证**: 需要，**管理员权限**
*   **请求体**:
    ```json
    {
      "name": "Ultra-High-Speed",
      "description": "超高速代理标签",
      "color": "#FF0000"
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Tag updated successfully",
      "data": {
        "id": "tag-uuid",
        "name": "Ultra-High-Speed",
        "description": "超高速代理标签",
        "color": "#FF0000",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    }
    ```

### 4. 删除标签

*   **DELETE** `/tags/:id`
*   **描述**: 删除指定的标签
*   **认证**: 需要，**管理员权限**
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Tag deleted successfully"
    }
    ```

### 5. 为代理分配标签

*   **POST** `/tags/assign`
*   **描述**: 为一个或多个代理分配一个或多个标签
*   **认证**: 需要，**管理员权限**
*   **请求体**:
    ```json
    {
      "proxy_ids": ["proxy-uuid-1", "proxy-uuid-2"],
      "tag_ids": ["tag-uuid-1", "tag-uuid-2"]
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Tag assignment completed",
      "success": 4,
      "total": 4,
      "details": {
        "proxy_uuid_1": ["tag-uuid-1", "tag-uuid-2"],
        "proxy_uuid_2": ["tag-uuid-1", "tag-uuid-2"]
      }
    }
    ```

### 6. 移除代理标签

*   **POST** `/tags/unassign`
*   **描述**: 从代理中移除指定标签
*   **认证**: 需要，**管理员权限**
*   **请求体**:
    ```json
    {
      "proxy_ids": ["proxy-uuid-1"],
      "tag_ids": ["tag-uuid-1"]
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Tag unassignment completed",
      "success": 1,
      "total": 1
    }
    ```

---

## 📋 任务管理 (Task Management)

### 1. 获取用户任务列表

*   **GET** `/tasks`
*   **描述**: 获取当前用户的所有任务列表
*   **认证**: 需要
*   **查询参数**:
    - `page`: 页码 (默认: 1)
    - `limit`: 每页数量 (默认: 20)
    - `status`: 状态筛选 (`pending`, `running`, `completed`, `failed`, `cancelled`)
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "task-uuid",
          "name": "Web Scraping Task",
          "url": "https://example.com",
          "method": "GET",
          "status": "completed",
          "priority": 2,
          "retry_count": 0,
          "max_retries": 3,
          "result": "Success",
          "created_at": "2024-01-15T10:00:00Z",
          "started_at": "2024-01-15T10:01:00Z",
          "completed_at": "2024-01-15T10:02:00Z",
          "proxy_id": "proxy-uuid"
        }
      ],
      "pagination": {
        "page": 1,
        "limit": 20,
        "total": 50,
        "pages": 3
      }
    }
    ```

### 2. 创建任务

*   **POST** `/tasks`
*   **描述**: 创建一个新的任务
*   **认证**: 需要
*   **请求体**:
    ```json
    {
      "name": "My Scraping Task",
      "url": "https://example.com/api/data",
      "method": "GET",
      "headers": {
        "User-Agent": "Mozilla/5.0...",
        "Accept": "application/json"
      },
      "body": "",
      "proxy_strategy": "round_robin",
      "priority": 2,
      "max_retries": 3,
      "timeout": 30
    }
    ```
*   **响应 (201 Created)**:
    ```json
    {
      "message": "Task created successfully",
      "data": {
        "id": "new-task-uuid",
        "name": "My Scraping Task",
        "status": "pending",
        "priority": 2,
        "created_at": "2024-01-15T10:30:00Z"
      }
    }
    ```

### 3. 获取任务详情

*   **GET** `/tasks/:id`
*   **描述**: 获取指定任务的详细信息
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "id": "task-uuid",
        "name": "Web Scraping Task",
        "url": "https://example.com",
        "method": "GET",
        "headers": {
          "User-Agent": "Mozilla/5.0..."
        },
        "status": "completed",
        "priority": 2,
        "retry_count": 0,
        "max_retries": 3,
        "result": "Success - Data retrieved",
        "error": "",
        "created_at": "2024-01-15T10:00:00Z",
        "started_at": "2024-01-15T10:01:00Z",
        "completed_at": "2024-01-15T10:02:00Z",
        "proxy_id": "proxy-uuid",
        "user_id": "user-uuid"
      }
    }
    ```

### 4. 更新任务

*   **PUT** `/tasks/:id`
*   **描述**: 更新指定任务的信息
*   **认证**: 需要
*   **请求体**:
    ```json
    {
      "name": "Updated Task Name",
      "priority": 3,
      "max_retries": 5
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Task updated successfully",
      "data": {
        "id": "task-uuid",
        "name": "Updated Task Name",
        "priority": 3,
        "max_retries": 5,
        "updated_at": "2024-01-15T10:30:00Z"
      }
    }
    ```

### 5. 取消任务

*   **PATCH** `/tasks/:id/cancel`
*   **描述**: 取消一个正在进行或等待中的任务
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Task cancelled successfully"
    }
    ```

### 6. 删除任务

*   **DELETE** `/tasks/:id`
*   **描述**: 删除指定的任务
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Task deleted successfully"
    }
    ```

### 7. 获取任务统计

*   **GET** `/tasks/stats`
*   **描述**: 获取当前用户的任务统计信息
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "total": 100,
        "pending": 5,
        "running": 2,
        "completed": 85,
        "failed": 6,
        "cancelled": 2,
        "success_rate": 0.85
      }
    }
    ```

### 8. 批量删除任务

*   **POST** `/tasks/batch-delete`
*   **描述**: 批量删除多个任务
*   **认证**: 需要
*   **请求体**:
    ```json
    {
      "task_ids": ["task-uuid-1", "task-uuid-2", "task-uuid-3"]
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Batch delete completed",
      "success": 3,
      "failed": 0,
      "errors": []
    }
    ```

### 9. 批量取消任务

*   **POST** `/tasks/batch-cancel`
*   **描述**: 批量取消多个任务
*   **认证**: 需要
*   **请求体**:
    ```json
    {
      "task_ids": ["task-uuid-1", "task-uuid-2"]
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Batch cancel completed",
      "success": 2,
      "failed": 0,
      "errors": []
    }
    ```

---

## 🔄 代理采集器管理 (Collector Management)

### 1. 获取采集器状态

*   **GET** `/collector/status`
*   **描述**: 获取代理采集器的当前状态
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "running": true,
        "last_collection": "2024-01-15T10:30:00Z",
        "total_sources": 45,
        "success_sources": 42,
        "total_proxies": 1250,
        "valid_proxies": 387,
        "duplicate_count": 125,
        "success_rate": 30.96,
        "collection_duration": "2m15s"
      }
    }
    ```

### 2. 获取采集器统计

*   **GET** `/collector/stats`
*   **描述**: 获取采集器的详细统计信息
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "total_collections": 150,
        "avg_success_rate": 0.32,
        "avg_duration": "2m30s",
        "total_proxies_collected": 50000,
        "total_valid_proxies": 16000,
        "last_24h": {
          "collections": 12,
          "proxies_collected": 2400,
          "valid_proxies": 768
        }
      }
    }
    ```

### 3. 获取采集器指标

*   **GET** `/collector/metrics`
*   **描述**: 获取采集器的性能指标
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "is_running": true,
        "last_collection": "2024-01-15T10:30:00Z",
        "total_collections": 150,
        "avg_success_rate": 0.32,
        "avg_duration": "2m30s",
        "total_proxies_collected": 50000,
        "total_valid_proxies": 16000
      }
    }
    ```

### 4. 获取采集历史

*   **GET** `/collector/history`
*   **描述**: 获取采集器的历史记录
*   **认证**: 需要
*   **查询参数**:
    - `limit`: 返回记录数量 (默认: 10)
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": "collection-uuid",
          "started_at": "2024-01-15T10:00:00Z",
          "completed_at": "2024-01-15T10:02:15Z",
          "duration": "2m15s",
          "total_proxies": 1250,
          "valid_proxies": 387,
          "success_rate": 0.31,
          "status": "completed"
        }
      ],
      "total": 150
    }
    ```

### 5. 手动触发采集

*   **POST** `/collector/collect`
*   **描述**: 立即执行一次代理采集任务
*   **认证**: 需要，**管理员权限**
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "message": "Collection started successfully",
      "data": {
        "task_id": "collection-task-uuid",
        "estimated_duration": "3 minutes"
      }
    }
    ```

### 6. 启动采集器

*   **POST** `/collector/start`
*   **描述**: 启动代理采集器服务
*   **认证**: 需要，**管理员权限**
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "message": "Collector started successfully"
    }
    ```

### 7. 停止采集器

*   **POST** `/collector/stop`
*   **描述**: 停止代理采集器服务
*   **认证**: 需要，**管理员权限**
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "message": "Collector stopped successfully"
    }
    ```

### 8. 启动独立采集任务

*   **POST** `/collector/standalone`
*   **描述**: 启动独立的代理采集任务，适用于大规模代理验证
*   **认证**: 需要，**管理员权限**
*   **请求体**:
    ```json
    {
      "priority": 3,
      "immediate": true,
      "force": false,
      "batch_size": 1000,
      "concurrent_workers": 200
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "message": "Standalone collection task started",
      "data": {
        "task_id": "standalone-task-uuid",
        "estimated_duration": "10 minutes"
      }
    }
    ```

## 采集器任务管理

### 1. 获取采集任务列表

*   **GET** `/collector/tasks`
*   **描述**: 获取所有采集任务列表
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": "task-uuid",
          "status": "running",
          "priority": 3,
          "progress": 45.5,
          "created_at": "2024-01-15T10:00:00Z",
          "started_at": "2024-01-15T10:01:00Z"
        }
      ],
      "total": 5
    }
    ```

### 2. 获取任务详情

*   **GET** `/collector/tasks/:taskId`
*   **描述**: 获取指定采集任务的详细信息
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "id": "task-uuid",
        "status": "running",
        "priority": 3,
        "progress": 45.5,
        "processed_count": 455,
        "total_count": 1000,
        "speed": 12.5,
        "eta": "5m30s",
        "created_at": "2024-01-15T10:00:00Z",
        "started_at": "2024-01-15T10:01:00Z"
      }
    }
    ```

### 3. 获取任务进度

*   **GET** `/collector/tasks/:taskId/progress`
*   **描述**: 获取指定任务的实时进度信息
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "task_id": "task-uuid",
        "phase": "verification",
        "progress_percent": 45.5,
        "processed_count": 455,
        "total_count": 1000,
        "speed": 12.5,
        "eta": "5m30s",
        "status": "running"
      }
    }
    ```

---

## ⚙️ 用户设置管理 (User Settings)

### 1. 获取用户设置

*   **GET** `/user/settings`
*   **描述**: 获取当前用户的所有设置
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "profile": {
          "display_name": "John Doe",
          "timezone": "UTC+8",
          "language": "zh-CN"
        },
        "preferences": {
          "theme": "dark",
          "notifications": true,
          "auto_refresh": 30
        },
        "security": {
          "two_factor_enabled": false,
          "session_timeout": 3600
        }
      }
    }
    ```

### 2. 更新用户设置

*   **PUT** `/user/settings`
*   **描述**: 更新用户设置
*   **认证**: 需要
*   **请求体**:
    ```json
    {
      "profile": {
        "display_name": "John Smith",
        "timezone": "UTC+8"
      },
      "preferences": {
        "theme": "light",
        "notifications": false
      }
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "message": "Settings updated successfully"
    }
    ```

### 3. 重置用户设置

*   **POST** `/user/settings/reset`
*   **描述**: 重置用户设置为默认值
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "message": "Settings reset to default values"
    }
    ```

### 4. 获取代理设置

*   **GET** `/user/proxy-settings`
*   **描述**: 获取用户的代理相关设置
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "defaultSettings": {
          "timeout": 30,
          "maxRetries": 3,
          "strategy": "round_robin"
        },
        "healthCheck": {
          "enabled": true,
          "interval": 300,
          "timeout": 10
        }
      }
    }
    ```

### 5. 更新代理设置

*   **PUT** `/user/proxy-settings`
*   **描述**: 更新用户的代理设置
*   **认证**: 需要
*   **请求体**:
    ```json
    {
      "defaultSettings": {
        "timeout": 60,
        "maxRetries": 5,
        "strategy": "least_used"
      },
      "healthCheck": {
        "enabled": true,
        "interval": 600,
        "timeout": 15
      }
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "message": "Proxy settings updated successfully"
    }
    ```

---

## 📊 系统信息和监控

### 1. 系统健康检查

*   **GET** `/health`
*   **描述**: 获取系统健康状态
*   **认证**: 无需
*   **响应 (200 OK)**:
    ```json
    {
      "status": "ok",
      "timestamp": "2024-01-15T10:30:00Z",
      "services": {
        "database": "healthy",
        "collector": "running"
      }
    }
    ```

### 2. Prometheus 指标

*   **GET** `/metrics`
*   **描述**: 获取 Prometheus 格式的系统指标
*   **认证**: 无需
*   **响应 (200 OK)**:
    ```text
    # HELP proxy_total Total number of proxies
    # TYPE proxy_total gauge
    proxy_total 150

    # HELP proxy_active_total Number of active proxies
    # TYPE proxy_active_total gauge
    proxy_active_total 120
    ```

---

## 🔗 API 使用示例

### 使用 JWT Token 认证

```bash
# 1. 用户登录获取 Token
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "your_password"
  }'

# 2. 使用 Token 访问 API
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8080/api/v1/proxies
```

### 使用 API Key 认证

```bash
# 1. 创建 API Key (需要先用 JWT Token 登录)
curl -X POST http://localhost:8080/api/v1/user/apikeys \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My API Key",
    "expires_in_days": 365
  }'

# 2. 使用 API Key 访问 API
curl -H "Authorization: Bearer YOUR_API_KEY" \
  http://localhost:8080/api/v1/proxies
```

### 获取高质量代理

```bash
# 获取质量分数大于 0.8 的代理
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8080/api/v1/proxies/quality?min_score=0.8"

# 获取美国地区的代理
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8080/api/v1/proxies/location?country_code=US"

# 使用智能路由获取最佳代理
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8080/api/v1/proxies/smart-routing?scenario=web_scraping&strategy=best_quality"
```

---

## 📝 错误响应格式

所有 API 错误都遵循统一的响应格式：

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "Additional error details"
  }
}
```

### 常见错误代码

- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未认证或认证失败
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `429 Too Many Requests`: 请求频率超限
- `500 Internal Server Error`: 服务器内部错误

---

## 📚 更多信息

- **项目文档**: [docs/](../docs/)
- **设置指南**: [SETUP_GUIDE.md](SETUP_GUIDE.md)
- **采集器指南**: [COLLECTOR_GUIDE.md](COLLECTOR_GUIDE.md)
- **独立采集器**: [COLLECTOR_STANDALONE.md](COLLECTOR_STANDALONE.md)
