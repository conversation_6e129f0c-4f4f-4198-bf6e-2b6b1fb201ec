请根据当前项目情况，阅读项目源码并完成以下目标。
在 @freeProxy/sources.json 文件有代理源信息，你可以阅读该文件。然后帮我实现以下目标：
1. 代理源包含了各种类型的代理，你应该对每个代理源发起请求，获取实际的代理，然后根据实际情况存储到某个文件中。
2. 有相应的检测逻辑，检测获取到的代理是否可用，延迟信息之类的。
3. 将检测到可用的代理添加到本项目的后端服务中。
4. 这个逻辑最好做成一个独立的模块，独立的文件夹之类的。
5. 这个服务应该不影响程序主流程运行，或许可以用一个goroutine。
6. 增加你认为应该增加的逻辑。

------------------------------------------------------------------------------------
文件 @b:\conan-work\proxyFlow/internal\collector\verifier.go 有个验证代理是否有效的逻辑：verifyProxies 但是验证的代理数量可能超过10万条，如何才能做到更加高效的验证，并且能有合适的进度提示。最好还有个本地文件能简单记录上次验证的时间，如果没有超过24小时则不用重复验证。


------------------------------------------------------------------------------------
我觉得目前的方案可能稍微有点复杂了，我理解这个需求代码量不应该有这么多。
应该在不影响原有功能的前提增加新功能。请帮我精简代码，实现刚刚的需求。最后不需要测试文件，只需要通过go run cmd/main.go 来验证。


------------------------------------------------------------------------------------
当前项目是一个基于 golang 的代理池项目，有一个 collector 收集器功能，能收集并且检测代理有效性能。具体逻辑位于： @/Users/<USER>/Desktop/conan-work/proxyManager/internal/collector/ 。
请帮我阅读源码，然后调研 golang 有什么比较好用的命令行显示方案，我需要人性化的显示 collector  verifyProxies 检验代理是否有效的过程，需要动态更新显示检测进度，预估时间等。
请帮我选择最成熟高效的方案，然后对代码进行修改


------------------------------------------------------------------------------------
项目入口位于： @/Users/<USER>/Desktop/conan-work/proxyManager/cmd/main.go  请帮我阅读相关源码，检查运行流程，目前的“检测代理”运行流程并没有看到刚刚的修改。


------------------------------------------------------------------------------------
请帮我阅读分析 @internal/app/application.go 中启动代理采集器的逻辑以及运行流程.
目前采集器和其他的BackgroundServices都是程序开始的时候后端异步运行,由于代理采集器需要检验接近200万代理的可连接性.
请帮我综合分析,这部分功能是否需要单独拆分出来?如果需要,那么如何拆分?拆分后如何调用?
请进行调研,修改过程中尽量考虑全面,细致.
提供一个思路: 将采集器的功能拆分到一个独立的模块中,然后可以直接通过 go run xxx 运行这个模块,或者/xxx 路由调用运行,然后把采集到的数据写入数据库, 这样是否就避免了耦合,也更加合理
修改进尽量简洁，不要过度涉及. 修改功能最好不要影响到其他功能. 确保程序能正确编译.



------------------------------------------------------------------------------------
当前项目有一个高并发实现模块 @/Users/<USER>/Desktop/conan-work/proxyManager/internal/concurrent/ 请帮我阅读相关源码，然后解决下列问题：
1. 检查这个高并发模块是否合理，是否有潜在漏洞
2. 检查相关使用到的地方，使用的方法是否合理正确
3. 这个模块是否有过度设计的嫌疑，如何精简（保证高效率的情况）
4. 如果可以，怎么精简这个模块，让其更易读，易调试。


------------------------------------------------------------------------------------
当前项目为一个`代理池`项目。后端基于 `golang` 实现，项目根目录为当前目录，项目入口为 `cmd/main.go` 与 `cmd/collector/main.go`。前端项目`代理池看板`服务基于 `React + Vite + Tailwind CSS + TypeScript` 实现，位于 `web` 目录。请帮我阅读相关源码，然后解决下列问题：
0. 核心目的是实现一个高性能、优秀的`代理池`项目。
1. 从程序入口开始，阅读、分析当前后端服务的功能实现。
2. 分析当前项目还缺少什么功能、多余了什么功能。并且列出你的具有优先级的改进建议。
3. 仔细阅读各个功能的实现。实现是否完善，正确，优雅。从 API 端点到数据库包括 postgresql 的流程是否正确、合理、高效。
4. 分析现有数据库包括 postgresql 的接入、作用是否正确、合理、高效。
5. 根据分析结果，规划改善步骤，每一步都有迹可循，逐渐达到最终优化目标。

------------------------------------------------------------------------------------
当前项目为一个`代理池`项目。后端基于 `golang` 实现，项目根目录为当前目录，项目入口为 `cmd/main.go` 与 `cmd/collector/main.go`。
前端项目`代理池看板`服务基于 `React + Vite + Tailwind CSS + TypeScript` 实现，位于 `web` 目录。
1. 请帮我深入阅读源码，查看后端项目的`api/routes`的实现，每个 API 端口对应的业务逻辑、接受参数、返回结果，以及数据库的操作是否正确、合理、高效。可以将分析结果更新到 docs/api.md 文档。
2. 再分析前端请求逻辑`web/src/services`是否跟后端 API 对应正确（端口、参数、方法、返回结果等）。
3. 项目的核心目标是一个`高效、高性能的代理池`，帮我分析是否有暂时不需要的 API 端口，如果有可以删除相应的前后端逻辑。 



------------------------------------------------------------------------------------




------------------------------------------------------------------------------------


