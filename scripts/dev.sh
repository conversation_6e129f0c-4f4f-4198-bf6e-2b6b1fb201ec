#!/bin/bash

# 开发环境脚本 - 启动/停止 PostgreSQL

set -e

# 配置
NETWORK="proxy-manager-network"
POSTGRES_CONTAINER="proxy-manager-postgres"

# 颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}$1${NC}"
}

error() {
    echo -e "${RED}$1${NC}"
}

warn() {
    echo -e "${YELLOW}$1${NC}"
}

# 检查 Docker
check_docker() {
    if ! command -v docker >/dev/null 2>&1; then
        error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
}

# 检查容器是否存在
container_exists() {
    local container_name="$1"
    docker ps -a --format "table {{.Names}}" | grep -q "^${container_name}$"
}

# 检查容器是否正在运行
container_running() {
    local container_name="$1"
    docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"
}

# 启动或创建容器
start_or_create_container() {
    local container_name="$1"
    local image="$2"
    shift 2
    local run_args=("$@")
    
    if container_exists "$container_name"; then
        if container_running "$container_name"; then
            log "$container_name 已在运行"
        else
            log "启动已存在的容器: $container_name"
            docker start "$container_name"
        fi
    else
        log "创建并启动新容器: $container_name"
        docker run --name "$container_name" "${run_args[@]}" -d "$image"
    fi
}

# 创建网络
create_network() {
    if ! docker network ls | grep -q "$NETWORK"; then
        log "创建网络: $NETWORK"
        docker network create "$NETWORK"
    fi
}

# 启动服务
start_services() {
    check_docker
    create_network
    
    log "启动 PostgreSQL..."
    # 从环境变量获取数据库密码，如果没有则使用默认值
    DB_PASSWORD="${PROXY_MANAGER_POSTGRES_PASSWORD:-proxy_manager_2025}"
    start_or_create_container "$POSTGRES_CONTAINER" "postgres" \
        -p 5432:5432 \
        --network "$NETWORK" \
        --restart unless-stopped \
        -e POSTGRES_PASSWORD="$DB_PASSWORD" \
        -e POSTGRES_DB=proxy_manager
    
    log "服务启动完成！"
    log "PostgreSQL: localhost:5432"
}

# 停止服务
stop_services() {
    log "停止服务..."
    docker stop "$POSTGRES_CONTAINER" 2>/dev/null || warn "PostgreSQL 未运行"
    log "服务已停止"
}

# 主逻辑
case "${1:-start}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    *)
        error "用法: $0 [start|stop]"
        exit 1
        ;;
esac 