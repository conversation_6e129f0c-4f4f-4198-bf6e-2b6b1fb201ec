#!/bin/bash

# ProxyFlow 一键部署脚本
# 支持开发环境和生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔧 $1${NC}"; }

# 进入项目根目录
cd "$(dirname "$0")/.."

# 默认参数
ENVIRONMENT="development"
SKIP_INIT=false
SKIP_BUILD=false
DETACHED=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --detached|-d)
            DETACHED=true
            shift
            ;;
        --help|-h)
            echo "ProxyFlow 一键部署脚本"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --env ENV        部署环境 (development|production) [默认: development]"
            echo "  --skip-init      跳过初始化步骤"
            echo "  --skip-build     跳过构建步骤"
            echo "  --detached, -d   后台运行服务"
            echo "  --help, -h       显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                           # 开发环境部署"
            echo "  $0 --env production          # 生产环境部署"
            echo "  $0 --env production -d       # 生产环境后台部署"
            echo "  $0 --skip-init --skip-build  # 跳过初始化和构建"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                ProxyFlow 一键部署                          ║"
echo "║                环境: $ENVIRONMENT                              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 1. 初始化项目
if [ "$SKIP_INIT" = false ]; then
    log_step "步骤 1/5: 项目初始化"
    if [ ! -f ".env" ]; then
        log_info "运行初始化脚本..."
        ./scripts/init.sh
    else
        log_info "项目已初始化，跳过初始化步骤"
        log_info "如需重新初始化，请删除 .env 文件后重新运行"
    fi
else
    log_step "步骤 1/5: 跳过项目初始化"
fi

# 2. 启动数据库服务
log_step "步骤 2/5: 启动数据库服务"
log_info "启动 PostgreSQL..."
./scripts/dev.sh start

# 等待数据库启动
log_info "等待数据库服务启动..."
sleep 3

# 3. 验证配置
log_step "步骤 3/5: 验证配置"
if [ -f "scripts/validate_config.go" ]; then
    log_info "运行配置验证..."
    if ! go run scripts/validate_config.go; then
        log_error "配置验证失败"
        exit 1
    fi
else
    log_warning "配置验证脚本不存在，跳过验证"
fi

# 4. 构建项目
if [ "$SKIP_BUILD" = false ]; then
    log_step "步骤 4/5: 构建项目"
    
    # 构建后端
    log_info "构建后端..."
    go mod tidy
    go build -o bin/proxymanager cmd/main.go
    log_success "后端构建完成"
    
    # 构建前端
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "构建前端 (生产环境)..."
        cd web
        if command -v pnpm >/dev/null 2>&1; then
            pnpm install --frozen-lockfile
            pnpm run build
        else
            npm ci
            npm run build
        fi
        cd ..
        log_success "前端构建完成"
    else
        log_info "开发环境，跳过前端构建"
    fi
else
    log_step "步骤 4/5: 跳过构建步骤"
fi

# 5. 启动服务
log_step "步骤 5/5: 启动服务"

if [ "$ENVIRONMENT" = "production" ]; then
    # 生产环境
    log_info "启动生产环境服务..."
    
    if [ "$DETACHED" = true ]; then
        log_info "后台启动服务..."
        nohup ./bin/proxymanager > logs/app.log 2>&1 &
        echo $! > proxymanager.pid
        log_success "服务已在后台启动 (PID: $(cat proxymanager.pid))"
        log_info "日志文件: logs/app.log"
        log_info "停止服务: kill $(cat proxymanager.pid)"
    else
        log_info "前台启动服务..."
        ./bin/proxymanager
    fi
else
    # 开发环境
    log_info "启动开发环境服务..."
    
    if [ "$DETACHED" = true ]; then
        log_info "后台启动后端服务..."
        nohup go run cmd/main.go > logs/backend.log 2>&1 &
        echo $! > backend.pid
        
        log_info "后台启动前端服务..."
        cd web
        if command -v pnpm >/dev/null 2>&1; then
            nohup pnpm run dev > ../logs/frontend.log 2>&1 &
        else
            nohup npm run dev > ../logs/frontend.log 2>&1 &
        fi
        echo $! > ../frontend.pid
        cd ..
        
        log_success "开发服务已在后台启动"
        log_info "后端 PID: $(cat backend.pid), 日志: logs/backend.log"
        log_info "前端 PID: $(cat frontend.pid), 日志: logs/frontend.log"
        log_info "停止服务: kill $(cat backend.pid) $(cat frontend.pid)"
    else
        log_info "前台启动后端服务..."
        log_warning "请在新终端中运行前端: cd web && npm run dev"
        go run cmd/main.go
    fi
fi

# 部署完成信息
echo ""
echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    🎉 部署完成！                              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

echo -e "${CYAN}🌐 访问地址:${NC}"
if [ "$ENVIRONMENT" = "production" ]; then
    echo "• 应用地址: http://localhost:8080"
    echo "• API 文档: http://localhost:8080/api/docs"
    echo "• 监控指标: http://localhost:8080/metrics"
else
    echo "• 后端 API: http://localhost:8080"
    echo "• 前端开发: http://localhost:5173"
    echo "• API 文档: http://localhost:8080/api/docs"
fi

echo ""
echo -e "${CYAN}📋 管理员信息:${NC}"
if [ -f ".env" ]; then
    ADMIN_USER=$(grep "PROXY_MANAGER_ADMIN_USERNAME" .env | cut -d'"' -f2 2>/dev/null || echo "admin")
    ADMIN_PASS=$(grep "PROXY_MANAGER_ADMIN_PASSWORD" .env | cut -d'"' -f2 2>/dev/null || echo "请查看 .env 文件")
    echo "• 用户名: $ADMIN_USER"
    echo "• 密码: $ADMIN_PASS"
else
    echo "• 请查看 .env 文件获取管理员信息"
fi

echo ""
echo -e "${YELLOW}💡 常用命令:${NC}"
echo "• 停止数据库: ./scripts/dev.sh stop"
echo "• 查看日志: tail -f logs/app.log"
echo "• 配置验证: go run scripts/validate_config.go"
echo "• 导入代理: go run scripts/import_webshare_proxies.go"
