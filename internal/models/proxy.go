package models

import (
	"time"
)

// ProxyType 代理类型
type ProxyType string

const (
	ProxyTypeHTTP   ProxyType = "http"
	ProxyTypeHTTPS  ProxyType = "https"
	ProxyTypeSOCKS5 ProxyType = "socks5"
)

// ProxyStatus 代理状态
type ProxyStatus string

const (
	ProxyStatusActive   ProxyStatus = "active"
	ProxyStatusInactive ProxyStatus = "inactive"
	ProxyStatusFailed   ProxyStatus = "failed"
)

// AnonymityLevel 匿名级别
type AnonymityLevel string

const (
	AnonymityLevelTransparent AnonymityLevel = "transparent" // 透明代理
	AnonymityLevelAnonymous   AnonymityLevel = "anonymous"   // 匿名代理
	AnonymityLevelElite       AnonymityLevel = "elite"       // 高匿代理
	AnonymityLevelUnknown     AnonymityLevel = "unknown"     // 未知级别
)

// Proxy 代理模型
type Proxy struct {
	ID           string      `json:"id" db:"id"`
	Host         string      `json:"host" db:"host"`
	Port         int         `json:"port" db:"port"`
	Type         ProxyType   `json:"type" db:"type"`
	Username     string      `json:"username,omitempty" db:"username"`
	Password     string      `json:"password,omitempty" db:"password"`
	Status       ProxyStatus `json:"status" db:"status"`
	Failures     int         `json:"failures" db:"failures"`
	LastCheck    *time.Time  `json:"last_check,omitempty" db:"last_check"`
	LastSuccess  *time.Time  `json:"last_success,omitempty" db:"last_success"`
	CreatedAt    time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time   `json:"updated_at" db:"updated_at"`
	UseCount     int64       `json:"use_count" db:"use_count"`
	ResponseTime *int64      `json:"response_time,omitempty" db:"response_time"` // 毫秒
	Weight       int         `json:"weight" db:"weight"`                         // 权重，用于加权轮询
	// 地理位置信息
	CountryCode           string `json:"country_code,omitempty" db:"country_code"`
	CityName              string `json:"city_name,omitempty" db:"city_name"`
	ASNName               string `json:"asn_name,omitempty" db:"asn_name"`
	ASNNumber             *int   `json:"asn_number,omitempty" db:"asn_number"`
	HighCountryConfidence bool   `json:"high_country_confidence" db:"high_country_confidence"`
	// 质量评估信息
	QualityScore     float64    `json:"quality_score" db:"quality_score"`
	SpeedScore       float64    `json:"speed_score" db:"speed_score"`
	StabilityScore   float64    `json:"stability_score" db:"stability_score"`
	AnonymityLevel   string     `json:"anonymity_level" db:"anonymity_level"`
	ReliabilityScore float64    `json:"reliability_score" db:"reliability_score"`
	LastQualityCheck *time.Time `json:"last_quality_check,omitempty" db:"last_quality_check"`
	// 场景化分组信息
	Scenario string      `json:"scenario,omitempty" db:"scenario"`
	Priority int         `json:"priority" db:"priority"`
	Tags     []*ProxyTag `json:"tags,omitempty" db:"-"` // 关联的标签，不存储在数据库中
}

// ProxyRequest 添加代理请求
type ProxyRequest struct {
	Host     string    `json:"host" binding:"required"`
	Port     int       `json:"port" binding:"required,min=1,max=65535"`
	Type     ProxyType `json:"type" binding:"required,oneof=http https socks5"`
	Username string    `json:"username,omitempty"`
	Password string    `json:"password,omitempty"`
	Weight   int       `json:"weight,omitempty"`
	// 地理位置信息（可选）
	CountryCode           string `json:"country_code,omitempty"`
	CityName              string `json:"city_name,omitempty"`
	ASNName               string `json:"asn_name,omitempty"`
	ASNNumber             int    `json:"asn_number,omitempty"`
	HighCountryConfidence bool   `json:"high_country_confidence,omitempty"`
	// 质量评估信息
	AnonymityLevel string `json:"anonymity_level,omitempty"` // 匿名级别: transparent, anonymous, elite, unknown
	// 场景化分组信息
	Scenario string   `json:"scenario,omitempty"`
	Priority int      `json:"priority,omitempty"`
	TagIDs   []string `json:"tag_ids,omitempty"` // 要分配的标签ID列表
}

// ProxyResponse 代理响应
type ProxyResponse struct {
	ID           string      `json:"id"`
	Host         string      `json:"host"`
	Port         int         `json:"port"`
	Type         ProxyType   `json:"type"`
	Status       ProxyStatus `json:"status"`
	LastCheck    time.Time   `json:"last_check"`
	LastSuccess  time.Time   `json:"last_success"`
	UseCount     int64       `json:"use_count"`
	ResponseTime int64       `json:"response_time"`
	Weight       int         `json:"weight"`
	// 地理位置信息
	CountryCode           string `json:"country_code,omitempty"`
	CityName              string `json:"city_name,omitempty"`
	ASNName               string `json:"asn_name,omitempty"`
	ASNNumber             int    `json:"asn_number,omitempty"`
	HighCountryConfidence bool   `json:"high_country_confidence"`
}

// ProxyStats 代理统计
type ProxyStats struct {
	Total    int `json:"total"`
	Active   int `json:"active"`
	Inactive int `json:"inactive"`
	Failed   int `json:"failed"`
	HTTP     int `json:"http"`
	HTTPS    int `json:"https"`
	SOCKS5   int `json:"socks5"`
}

// LocationStats 地理位置统计
type LocationStats struct {
	Location    string  `json:"location"`     // 位置名称（国家或城市）
	CountryCode string  `json:"country_code"` // 国家代码
	CityName    string  `json:"city_name"`    // 城市名称
	Total       int     `json:"total"`        // 总数
	Active      int     `json:"active"`       // 活跃数
	Inactive    int     `json:"inactive"`     // 非活跃数
	Failed      int     `json:"failed"`       // 失败数
	AvgResponse float64 `json:"avg_response"` // 平均响应时间
	SuccessRate float64 `json:"success_rate"` // 成功率
}

// ProxyLocationStats 代理地理位置统计
type ProxyLocationStats struct {
	Countries []LocationStats `json:"countries"` // 按国家统计
	Cities    []LocationStats `json:"cities"`    // 按城市统计
	Unknown   LocationStats   `json:"unknown"`   // 未知位置统计
}

// ProxyGroupByLocation 按地理位置分组的代理
type ProxyGroupByLocation struct {
	Location string          `json:"location"` // 位置标识
	Name     string          `json:"name"`     // 位置名称
	Count    int             `json:"count"`    // 代理数量
	Proxies  []ProxyResponse `json:"proxies"`  // 代理列表
	Stats    LocationStats   `json:"stats"`    // 统计信息
}

// ProxyHealthCheck 健康检查结果
type ProxyHealthCheck struct {
	ProxyID      string      `json:"proxy_id"`
	Status       ProxyStatus `json:"status"`
	ResponseTime int64       `json:"response_time"`
	Error        string      `json:"error,omitempty"`
	CheckedAt    time.Time   `json:"checked_at"`
}

// QualityScore 质量评分结构
type QualityScore struct {
	Overall        float64 `json:"overall"`
	Speed          float64 `json:"speed"`
	Stability      float64 `json:"stability"`
	Reliability    float64 `json:"reliability"`
	AnonymityLevel string  `json:"anonymity_level"`
}

// HealthCheckRecord 健康检查记录
type HealthCheckRecord struct {
	ID                string    `json:"id" db:"id"`
	ProxyID           string    `json:"proxy_id" db:"proxy_id"`
	CheckTime         time.Time `json:"check_time" db:"check_time"`
	Status            string    `json:"status" db:"status"`
	ResponseTime      *int64    `json:"response_time,omitempty" db:"response_time"`
	ErrorMessage      string    `json:"error_message,omitempty" db:"error_message"`
	TestURL           string    `json:"test_url,omitempty" db:"test_url"`
	HTTPStatusCode    *int      `json:"http_status_code,omitempty" db:"http_status_code"`
	AnonymityDetected string    `json:"anonymity_detected,omitempty" db:"anonymity_detected"`
	RealIP            string    `json:"real_ip,omitempty" db:"real_ip"`
	DetectedLocation  string    `json:"detected_location,omitempty" db:"detected_location"`
}

// HealthCheckStats 健康检查统计
type HealthCheckStats struct {
	ProxyID         string        `json:"proxy_id"`
	Duration        time.Duration `json:"duration"`
	TotalChecks     int64         `json:"total_checks"`
	SuccessCount    int64         `json:"success_count"`
	FailedCount     int64         `json:"failed_count"`
	TimeoutCount    int64         `json:"timeout_count"`
	SuccessRate     float64       `json:"success_rate"`
	AvgResponseTime int64         `json:"avg_response_time"`
	MinResponseTime int64         `json:"min_response_time"`
	MaxResponseTime int64         `json:"max_response_time"`
}

// ProxyTag 代理标签
type ProxyTag struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description,omitempty" db:"description"`
	Color       string    `json:"color" db:"color"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	CreatedBy   *string   `json:"created_by,omitempty" db:"created_by"`
}

// ProxyTagAssignment 代理标签关联
type ProxyTagAssignment struct {
	ID         string    `json:"id" db:"id"`
	ProxyID    string    `json:"proxy_id" db:"proxy_id"`
	TagID      string    `json:"tag_id" db:"tag_id"`
	AssignedAt time.Time `json:"assigned_at" db:"assigned_at"`
	AssignedBy *string   `json:"assigned_by,omitempty" db:"assigned_by"`
}

// ProxyTagRequest 标签请求
type ProxyTagRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=50"`
	Description string `json:"description,omitempty"`
	Color       string `json:"color,omitempty"`
}

// ProxyTagAssignRequest 标签分配请求
type ProxyTagAssignRequest struct {
	ProxyIDs []string `json:"proxy_ids" binding:"required"`
	TagIDs   []string `json:"tag_ids" binding:"required"`
}

// ProxyFilterByTags 按标签筛选代理请求
type ProxyFilterByTags struct {
	Tags     []string `json:"tags,omitempty"`
	Scenario string   `json:"scenario,omitempty"`
	Strategy string   `json:"strategy,omitempty"` // 路由策略
}

// PaginationParams 分页参数
type PaginationParams struct {
	Page  int `json:"page" form:"page"`   // 页码，从1开始
	Limit int `json:"limit" form:"limit"` // 每页数量，默认20
}

// PaginatedResponse 分页响应
type PaginatedResponse struct {
	Data       interface{} `json:"data"`        // 数据列表
	Total      int64       `json:"total"`       // 总数量
	Page       int         `json:"page"`        // 当前页
	Limit      int         `json:"limit"`       // 每页数量
	TotalPages int         `json:"total_pages"` // 总页数
	HasNext    bool        `json:"has_next"`    // 是否有下一页
	HasPrev    bool        `json:"has_prev"`    // 是否有上一页
}

// ProxyListRequest 代理列表请求
type ProxyListRequest struct {
	PaginationParams
	Status      string `json:"status" form:"status"`             // 状态筛选
	Type        string `json:"type" form:"type"`                 // 类型筛选
	CountryCode string `json:"country_code" form:"country_code"` // 国家筛选
	Search      string `json:"search" form:"search"`             // 搜索关键词
}
