package models

import (
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// UserRole 用户角色
type UserRole string

const (
	UserRoleSuperAdmin UserRole = "super_admin"
	UserRoleAdmin      UserRole = "admin"
	UserRoleUser       UserRole = "user"
	UserRoleGuest      UserRole = "guest"
)

// UserStatus 用户状态
type UserStatus string

const (
	UserStatusActive   UserStatus = "active"
	UserStatusInactive UserStatus = "inactive"
	UserStatusBanned   UserStatus = "banned"
)

// User 用户模型
type User struct {
	ID               string     `json:"id" db:"id"`
	Username         string     `json:"username" db:"username"`
	Email            string     `json:"email" db:"email"`
	Password         string     `json:"password" db:"password_hash"` // 数据库中存储为password_hash
	Role             UserRole   `json:"role" db:"role"`
	IsActive         bool       `json:"-" db:"is_active"` // 数据库字段
	Status           UserStatus `json:"status" db:"-"`    // 计算字段，不存储在数据库中
	CreatedAt        time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at" db:"updated_at"`
	LastLogin        *time.Time `json:"last_login,omitempty" db:"last_login"`
	APIKey           string     `json:"api_key" db:"-"`             // API Key不存储在数据库中
	APIKeyCreated    time.Time  `json:"api_key_created" db:"-"`     // API Key创建时间不存储在数据库中
	APIKeyLastUsed   time.Time  `json:"api_key_last_used" db:"-"`   // API Key最后使用时间不存储在数据库中
	APIKeyUsageCount int64      `json:"api_key_usage_count" db:"-"` // API Key使用次数不存储在数据库中
}

// GetStatus 获取用户状态（从 IsActive 字段转换）
func (u *User) GetStatus() UserStatus {
	if u.IsActive {
		return UserStatusActive
	}
	return UserStatusInactive
}

// SetStatus 设置用户状态（转换为 IsActive 字段）
func (u *User) SetStatus(status UserStatus) {
	u.Status = status
	u.IsActive = (status == UserStatusActive)
}

// UserRequest 用户请求
type UserRequest struct {
	Username string   `json:"username" binding:"required,min=3,max=50"`
	Email    string   `json:"email" binding:"required,email"`
	Password string   `json:"password" binding:"required,min=6"`
	Role     UserRole `json:"role" binding:"required,oneof=admin user guest"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID        string     `json:"id"`
	Username  string     `json:"username"`
	Email     string     `json:"email"`
	Role      UserRole   `json:"role"`
	Status    UserStatus `json:"status"`
	CreatedAt time.Time  `json:"created_at"`
	LastLogin *time.Time `json:"last_login,omitempty"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	AccessToken  string       `json:"access_token"`
	RefreshToken string       `json:"refresh_token"`
	ExpiresIn    int64        `json:"expires_in"`
	User         UserResponse `json:"user"`
}

// RefreshTokenRequest 刷新token请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// Claims JWT声明
type Claims struct {
	UserID   string   `json:"user_id"`
	Username string   `json:"username"`
	Role     UserRole `json:"role"`
	Exp      int64    `json:"exp"`
	Iat      int64    `json:"iat"`
}

// GetExpirationTime 获取过期时间
func (c Claims) GetExpirationTime() (*jwt.NumericDate, error) {
	return jwt.NewNumericDate(time.Unix(c.Exp, 0)), nil
}

// GetNotBefore 获取生效时间
func (c Claims) GetNotBefore() (*jwt.NumericDate, error) {
	return jwt.NewNumericDate(time.Unix(c.Iat, 0)), nil
}

// GetIssuedAt 获取签发时间
func (c Claims) GetIssuedAt() (*jwt.NumericDate, error) {
	return jwt.NewNumericDate(time.Unix(c.Iat, 0)), nil
}

// GetIssuer 获取签发者
func (c Claims) GetIssuer() (string, error) {
	return "", nil
}

// GetSubject 获取主题
func (c Claims) GetSubject() (string, error) {
	return c.UserID, nil
}

// GetAudience 获取受众
func (c Claims) GetAudience() (jwt.ClaimStrings, error) {
	return jwt.ClaimStrings{}, nil
}

// APIKeyStatsResponse API Key 统计响应（兼容旧版本）
type APIKeyStatsResponse struct {
	APIKeyCreated    time.Time `json:"api_key_created"`
	APIKeyLastUsed   time.Time `json:"api_key_last_used"`
	APIKeyUsageCount int64     `json:"api_key_usage_count"`
	MaskedAPIKey     string    `json:"masked_api_key"`
}
