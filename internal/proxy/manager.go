package proxy

import (
	"context"
	"crypto/tls"
	"fmt"
	"math/rand"
	"net/http"
	"net/url"
	"sync"
	"sync/atomic"
	"time"

	"proxyFlow/internal/models"
	"proxyFlow/internal/repository"
	"proxyFlow/internal/utils"

	"github.com/sirupsen/logrus"
)

// Strategy 代理分配策略
type Strategy string

const (
	StrategyRoundRobin Strategy = "round_robin"
	StrategyLeastUsed  Strategy = "least_used"
	StrategyRandom     Strategy = "random"
	StrategyWeighted   Strategy = "weighted"
)

// Manager 代理管理器
type Manager struct {
	proxyRepo         repository.ProxyRepository
	proxyTagRepo      repository.ProxyTagRepository
	logger            *logrus.Logger
	config            *Config
	mu                sync.RWMutex
	roundRobinIndex   int64 // 使用原子操作访问
	qualityAssessment *QualityAssessment
}

// Config 代理管理器配置
type Config struct {
	HealthCheckInterval time.Duration
	MaxFailures         int
	Timeout             time.Duration
	MaxRetries          int
	// 并发健康检查配置
	ConcurrentWorkers int           // 并发工作者数量
	BatchTimeout      time.Duration // 批量检查超时时间
	RetryDelay        time.Duration // 重试延迟
}

// HealthCheckJob 健康检查任务
type HealthCheckJob struct {
	Proxy     *models.Proxy
	RetryLeft int
}

// HealthCheckResult 健康检查结果
type HealthCheckResult struct {
	Check *models.ProxyHealthCheck
	Error error
}

// ConcurrentHealthChecker 并发健康检查器
type ConcurrentHealthChecker struct {
	manager     *Manager
	workerCount int
	timeout     time.Duration
	retryDelay  time.Duration
	logger      *logrus.Logger
}

// NewManager 创建代理管理器
func NewManager(proxyRepo repository.ProxyRepository, proxyTagRepo repository.ProxyTagRepository, config *Config, logger *logrus.Logger) *Manager {
	// 设置默认并发配置
	if config.ConcurrentWorkers <= 0 {
		config.ConcurrentWorkers = 100 // 默认100个并发工作者
	}
	if config.BatchTimeout <= 0 {
		config.BatchTimeout = 30 * time.Second // 默认30秒超时
	}
	if config.RetryDelay <= 0 {
		config.RetryDelay = 1 * time.Second // 默认1秒重试延迟
	}

	return &Manager{
		proxyRepo:         proxyRepo,
		proxyTagRepo:      proxyTagRepo,
		logger:            logger,
		config:            config,
		qualityAssessment: NewQualityAssessment(logger),
	}
}

// AddProxy 添加代理
func (m *Manager) AddProxy(ctx context.Context, req *models.ProxyRequest) (*models.Proxy, error) {
	proxy := &models.Proxy{
		ID:        "", // 让数据库自动生成 UUID
		Host:      req.Host,
		Port:      req.Port,
		Type:      req.Type,
		Username:  req.Username,
		Password:  req.Password,
		Status:    models.ProxyStatusInactive,
		Failures:  0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		UseCount:  0,
		Weight:    req.Weight,
		// 地理位置信息
		CountryCode:           req.CountryCode,
		CityName:              req.CityName,
		ASNName:               req.ASNName,
		ASNNumber:             nil, // 先设置为nil，下面会根据条件设置
		HighCountryConfidence: req.HighCountryConfidence,
		// 质量评估信息 - 设置默认值
		QualityScore:     0.0,
		SpeedScore:       0.0,
		StabilityScore:   0.0,
		AnonymityLevel:   req.AnonymityLevel, // 使用请求中的值，如果为空则后面设置默认值
		ReliabilityScore: 0.0,
		// 场景化分组信息
		Scenario: req.Scenario,
		Priority: req.Priority,
	}

	if proxy.Weight == 0 {
		proxy.Weight = 1
	}

	if proxy.Priority == 0 {
		proxy.Priority = 1
	}

	// 设置默认的匿名级别
	if proxy.AnonymityLevel == "" {
		proxy.AnonymityLevel = "unknown"
	}

	// 正确处理 ASNNumber 字段
	if req.ASNNumber != 0 {
		proxy.ASNNumber = &req.ASNNumber
	}

	// 保存到PostgreSQL（会生成UUID）
	if err := m.proxyRepo.Create(ctx, proxy); err != nil {
		return nil, fmt.Errorf("save proxy to database error: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"proxy_id": proxy.ID,
		"host":     proxy.Host,
		"port":     proxy.Port,
		"type":     proxy.Type,
	}).Info("Proxy added")

	return proxy, nil
}

// GetProxy 获取代理
func (m *Manager) GetProxy(ctx context.Context, strategy Strategy) (*models.Proxy, error) {
	var proxyID string
	var err error

	switch strategy {
	case StrategyRoundRobin:
		proxyID, err = m.getRoundRobinProxy(ctx)
	case StrategyLeastUsed:
		// 这些策略需要读锁保护，因为涉及复杂的数据读取
		m.mu.RLock()
		proxyID, err = m.getLeastUsedProxy(ctx)
		m.mu.RUnlock()
	case StrategyRandom:
		proxyID, err = m.getRandomProxy(ctx)
	case StrategyWeighted:
		m.mu.RLock()
		proxyID, err = m.getWeightedProxy(ctx)
		m.mu.RUnlock()
	default:
		return nil, fmt.Errorf("unsupported strategy: %s", strategy)
	}

	if err != nil {
		return nil, err
	}

	proxy, err := m.getProxyByID(ctx, proxyID)
	if err != nil {
		return nil, err
	}

	// 更新使用次数
	proxy.UseCount++
	proxy.UpdatedAt = time.Now()
	if err := m.updateProxy(ctx, proxy); err != nil {
		m.logger.WithError(err).Error("Failed to update proxy use count")
	}

	return proxy, nil
}

// GetAvailableProxies 获取可用代理列表
func (m *Manager) GetAvailableProxies(ctx context.Context) ([]*models.Proxy, error) {
	// 直接从数据库获取活跃状态的代理
	return m.proxyRepo.GetByStatus(ctx, models.ProxyStatusActive)
}

// GetAllProxies 获取所有代理
func (m *Manager) GetAllProxies(ctx context.Context) ([]*models.Proxy, error) {
	// 直接从数据库获取所有代理
	return m.proxyRepo.List(ctx, repository.ProxyFilters{})
}

// DeleteProxy 删除代理
func (m *Manager) DeleteProxy(ctx context.Context, proxyID string) error {
	// 从数据库删除代理
	if err := m.proxyRepo.Delete(ctx, proxyID); err != nil {
		return fmt.Errorf("failed to delete proxy from database: %w", err)
	}

	m.logger.WithField("proxy_id", proxyID).Info("Proxy deleted")
	return nil
}

// GetProxiesByLocation 按地理位置获取代理
func (m *Manager) GetProxiesByLocation(ctx context.Context, countryCode, cityName string) ([]*models.Proxy, error) {
	allProxies, err := m.GetAllProxies(ctx)
	if err != nil {
		return nil, err
	}

	var filteredProxies []*models.Proxy
	for _, proxy := range allProxies {
		// 如果指定了国家代码，进行匹配
		if countryCode != "" && proxy.CountryCode != countryCode {
			continue
		}
		// 如果指定了城市名称，进行匹配
		if cityName != "" && proxy.CityName != cityName {
			continue
		}
		filteredProxies = append(filteredProxies, proxy)
	}

	return filteredProxies, nil
}

// GetProxyByID 通过ID从数据库获取代理
func (m *Manager) GetProxyByID(ctx context.Context, proxyID string) (*models.Proxy, error) {
	return m.proxyRepo.GetByID(ctx, proxyID)
}

// GetProxiesWithPagination 分页获取代理列表
func (m *Manager) GetProxiesWithPagination(ctx context.Context, req *models.ProxyListRequest) (*models.PaginatedResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// 构建过滤器
	filters := repository.ProxyFilters{}

	if req.Status != "" {
		status := models.ProxyStatus(req.Status)
		filters.Status = &status
	}

	if req.Type != "" {
		proxyType := models.ProxyType(req.Type)
		filters.Type = &proxyType
	}

	if req.CountryCode != "" {
		filters.CountryCode = &req.CountryCode
	}

	if req.Search != "" {
		filters.Search = &req.Search
	}

	// 调用仓储层分页查询
	proxies, total, err := m.proxyRepo.ListWithPagination(ctx, filters, req.PaginationParams)
	if err != nil {
		return nil, fmt.Errorf("failed to get proxies with pagination: %w", err)
	}

	// 转换为响应格式
	var responses []models.ProxyResponse
	for _, p := range proxies {
		response := models.ProxyResponse{
			ID:                    p.ID,
			Host:                  p.Host,
			Port:                  p.Port,
			Type:                  p.Type,
			Status:                p.Status,
			UseCount:              p.UseCount,
			Weight:                p.Weight,
			CountryCode:           p.CountryCode,
			CityName:              p.CityName,
			ASNName:               p.ASNName,
			HighCountryConfidence: p.HighCountryConfidence,
		}

		// 处理可能为nil的字段
		if p.LastCheck != nil {
			response.LastCheck = *p.LastCheck
		}
		if p.LastSuccess != nil {
			response.LastSuccess = *p.LastSuccess
		}
		if p.ResponseTime != nil {
			response.ResponseTime = *p.ResponseTime
		}
		if p.ASNNumber != nil {
			response.ASNNumber = *p.ASNNumber
		}

		responses = append(responses, response)
	}

	// 计算分页信息
	totalPages := int((total + int64(req.Limit) - 1) / int64(req.Limit))
	hasNext := req.Page < totalPages
	hasPrev := req.Page > 1

	return &models.PaginatedResponse{
		Data:       responses,
		Total:      total,
		Page:       req.Page,
		Limit:      req.Limit,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	}, nil
}

// GetProxyLocationStats 获取代理地理位置统计
func (m *Manager) GetProxyLocationStats(ctx context.Context) (*models.ProxyLocationStats, error) {
	allProxies, err := m.GetAllProxies(ctx)
	if err != nil {
		return nil, err
	}

	countryStats := make(map[string]*models.LocationStats)
	cityStats := make(map[string]*models.LocationStats)
	unknownStats := &models.LocationStats{
		Location: "unknown",
	}

	for _, proxy := range allProxies {
		// 处理国家统计
		if proxy.CountryCode != "" {
			key := proxy.CountryCode
			if countryStats[key] == nil {
				countryStats[key] = &models.LocationStats{
					Location:    proxy.CountryCode,
					CountryCode: proxy.CountryCode,
				}
			}
			m.updateLocationStats(countryStats[key], proxy)
		} else {
			// 未知位置统计
			m.updateLocationStats(unknownStats, proxy)
		}

		// 处理城市统计
		if proxy.CountryCode != "" && proxy.CityName != "" {
			key := proxy.CountryCode + "_" + proxy.CityName
			if cityStats[key] == nil {
				cityStats[key] = &models.LocationStats{
					Location:    proxy.CityName,
					CountryCode: proxy.CountryCode,
					CityName:    proxy.CityName,
				}
			}
			m.updateLocationStats(cityStats[key], proxy)
		}
	}

	// 转换为切片并计算平均值
	var countries []models.LocationStats
	for _, stats := range countryStats {
		m.calculateAverages(stats)
		countries = append(countries, *stats)
	}

	var cities []models.LocationStats
	for _, stats := range cityStats {
		m.calculateAverages(stats)
		cities = append(cities, *stats)
	}

	m.calculateAverages(unknownStats)

	return &models.ProxyLocationStats{
		Countries: countries,
		Cities:    cities,
		Unknown:   *unknownStats,
	}, nil
}

// updateLocationStats 更新位置统计信息
func (m *Manager) updateLocationStats(stats *models.LocationStats, proxy *models.Proxy) {
	stats.Total++
	switch proxy.Status {
	case models.ProxyStatusActive:
		stats.Active++
	case models.ProxyStatusInactive:
		stats.Inactive++
	case models.ProxyStatusFailed:
		stats.Failed++
	}

	// 累加响应时间用于计算平均值
	if proxy.ResponseTime != nil {
		stats.AvgResponse += float64(*proxy.ResponseTime)
	}
}

// calculateAverages 计算平均值
func (m *Manager) calculateAverages(stats *models.LocationStats) {
	if stats.Total > 0 {
		stats.AvgResponse = stats.AvgResponse / float64(stats.Total)
		stats.SuccessRate = float64(stats.Active) / float64(stats.Total) * 100
	}
}

// GroupProxiesByLocation 按地理位置分组代理
func (m *Manager) GroupProxiesByLocation(ctx context.Context, groupBy string) ([]models.ProxyGroupByLocation, error) {
	allProxies, err := m.GetAllProxies(ctx)
	if err != nil {
		return nil, err
	}

	groups := make(map[string]*models.ProxyGroupByLocation)

	for _, proxy := range allProxies {
		var key, name string

		switch groupBy {
		case "country":
			if proxy.CountryCode != "" {
				key = proxy.CountryCode
				name = proxy.CountryCode
			} else {
				key = "unknown"
				name = "未知位置"
			}
		case "city":
			if proxy.CountryCode != "" && proxy.CityName != "" {
				key = proxy.CountryCode + "_" + proxy.CityName
				name = proxy.CityName + " (" + proxy.CountryCode + ")"
			} else {
				key = "unknown"
				name = "未知位置"
			}
		default:
			key = "all"
			name = "全部"
		}

		if groups[key] == nil {
			groups[key] = &models.ProxyGroupByLocation{
				Location: key,
				Name:     name,
				Proxies:  []models.ProxyResponse{},
				Stats: models.LocationStats{
					Location:    name,
					CountryCode: proxy.CountryCode,
					CityName:    proxy.CityName,
				},
			}
		}

		// 添加代理到分组
		proxyResponse := models.ProxyResponse{
			ID:                    proxy.ID,
			Host:                  proxy.Host,
			Port:                  proxy.Port,
			Type:                  proxy.Type,
			Status:                proxy.Status,
			UseCount:              proxy.UseCount,
			Weight:                proxy.Weight,
			CountryCode:           proxy.CountryCode,
			CityName:              proxy.CityName,
			ASNName:               proxy.ASNName,
			HighCountryConfidence: proxy.HighCountryConfidence,
		}

		// 处理可能为 nil 的指针字段
		if proxy.LastCheck != nil {
			proxyResponse.LastCheck = *proxy.LastCheck
		}
		if proxy.LastSuccess != nil {
			proxyResponse.LastSuccess = *proxy.LastSuccess
		}
		if proxy.ResponseTime != nil {
			proxyResponse.ResponseTime = *proxy.ResponseTime
		}
		if proxy.ASNNumber != nil {
			proxyResponse.ASNNumber = *proxy.ASNNumber
		}

		groups[key].Proxies = append(groups[key].Proxies, proxyResponse)
		groups[key].Count++

		// 更新统计信息
		m.updateLocationStats(&groups[key].Stats, proxy)
	}

	// 转换为切片并计算平均值
	var result []models.ProxyGroupByLocation
	for _, group := range groups {
		m.calculateAverages(&group.Stats)
		result = append(result, *group)
	}

	return result, nil
}

// HealthCheck 健康检查
func (m *Manager) HealthCheck(ctx context.Context, proxy *models.Proxy) *models.ProxyHealthCheck {
	start := time.Now()

	check := &models.ProxyHealthCheck{
		ProxyID:   proxy.ID,
		CheckedAt: time.Now(),
	}

	// 测试代理连接
	err := m.testProxyConnection(proxy)
	if err != nil {
		check.Status = models.ProxyStatusFailed
		check.Error = err.Error()
		proxy.Failures++

		if proxy.Failures >= m.config.MaxFailures {
			proxy.Status = models.ProxyStatusFailed
		}
	} else {
		check.Status = models.ProxyStatusActive
		proxy.Status = models.ProxyStatusActive
		proxy.Failures = 0
		now := time.Now()
		proxy.LastSuccess = &now
	}

	check.ResponseTime = time.Since(start).Milliseconds()
	proxy.ResponseTime = &check.ResponseTime
	now := time.Now()
	proxy.LastCheck = &now
	proxy.UpdatedAt = now

	// 更新代理状态
	if err := m.updateProxy(ctx, proxy); err != nil {
		m.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to update proxy status HealthCheck")
	}

	return check
}

// StartHealthCheck 启动健康检查
func (m *Manager) StartHealthCheck(ctx context.Context) {
	ticker := time.NewTicker(m.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			m.runHealthCheck(ctx)
		}
	}
}

// BatchAddProxies 批量添加代理
func (m *Manager) BatchAddProxies(ctx context.Context, reqs []*models.ProxyRequest) (success, failed int, errors []error) {
	success = 0
	failed = 0
	errors = make([]error, 0)
	for _, req := range reqs {
		_, err := m.AddProxy(ctx, req)
		if err != nil {
			failed++
			errors = append(errors, err)
		} else {
			success++
		}
	}
	return
}

// 私有方法

func (m *Manager) getRoundRobinProxy(ctx context.Context) (string, error) {
	// 从数据库获取活跃代理
	proxies, err := m.proxyRepo.GetByStatus(ctx, models.ProxyStatusActive)
	if err != nil {
		return "", fmt.Errorf("failed to get active proxies: %w", err)
	}

	if len(proxies) == 0 {
		return "", fmt.Errorf("no available proxies")
	}

	// 使用原子操作安全地更新索引
	proxyCount := int64(len(proxies))
	index := atomic.AddInt64(&m.roundRobinIndex, 1) % proxyCount

	// 确保索引为正数
	if index < 0 {
		index = 0
		atomic.StoreInt64(&m.roundRobinIndex, 0)
	}

	return proxies[index].ID, nil
}

func (m *Manager) getLeastUsedProxy(ctx context.Context) (string, error) {
	proxies, err := m.GetAvailableProxies(ctx)
	if err != nil {
		return "", err
	}

	if len(proxies) == 0 {
		return "", fmt.Errorf("no available proxies")
	}

	var leastUsedProxy *models.Proxy
	for _, proxy := range proxies {
		if leastUsedProxy == nil || proxy.UseCount < leastUsedProxy.UseCount {
			leastUsedProxy = proxy
		}
	}

	return leastUsedProxy.ID, nil
}

func (m *Manager) getRandomProxy(ctx context.Context) (string, error) {
	// 从数据库获取活跃代理
	proxies, err := m.proxyRepo.GetByStatus(ctx, models.ProxyStatusActive)
	if err != nil {
		return "", fmt.Errorf("failed to get active proxies: %w", err)
	}

	if len(proxies) == 0 {
		return "", fmt.Errorf("no available proxies")
	}

	randomIndex := rand.Intn(len(proxies))
	return proxies[randomIndex].ID, nil
}

func (m *Manager) getWeightedProxy(ctx context.Context) (string, error) {
	proxies, err := m.GetAvailableProxies(ctx)
	if err != nil {
		return "", err
	}

	if len(proxies) == 0 {
		return "", fmt.Errorf("no available proxies")
	}

	// 计算总权重
	totalWeight := 0
	for _, proxy := range proxies {
		totalWeight += proxy.Weight
	}

	if totalWeight == 0 {
		return "", fmt.Errorf("no available proxies with weight")
	}

	// 随机选择
	random := rand.Intn(totalWeight)
	currentWeight := 0
	for _, proxy := range proxies {
		currentWeight += proxy.Weight
		if random < currentWeight {
			return proxy.ID, nil
		}
	}

	return proxies[0].ID, nil
}

func (m *Manager) getProxyByID(ctx context.Context, proxyID string) (*models.Proxy, error) {
	// 直接从数据库获取代理
	return m.proxyRepo.GetByID(ctx, proxyID)
}

func (m *Manager) updateProxy(ctx context.Context, proxy *models.Proxy) error {
	// 更新 PostgreSQL 数据库
	if err := m.proxyRepo.Update(ctx, proxy); err != nil {
		m.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to update proxy in database")
		return fmt.Errorf("failed to update proxy in database: %w", err)
	}

	return nil
}

func (m *Manager) testProxyConnection(proxy *models.Proxy) error {
	proxyURL := fmt.Sprintf("%s://%s:%d", proxy.Type, proxy.Host, proxy.Port)

	// 创建代理URL
	proxyURLParsed, err := url.Parse(proxyURL)
	if err != nil {
		return err
	}

	// 设置认证信息
	if proxy.Username != "" && proxy.Password != "" {
		proxyURLParsed.User = url.UserPassword(proxy.Username, proxy.Password)
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: m.config.Timeout,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyURLParsed),
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	// 测试请求
	testURL := utils.TEST_URLS[rand.Intn(len(utils.TEST_URLS))]
	m.logger.WithField("test_url", testURL).Info("Testing proxy connection")
	resp, err := client.Head(testURL) // 使用HEAD请求, 减少数据传输
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	m.logger.WithField("resp_length", resp.ContentLength).Info("client.Head(" + testURL + ")")

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("proxy returned status: %d", resp.StatusCode)
	}

	return nil
}

func (m *Manager) runHealthCheck(ctx context.Context) {
	proxies, err := m.GetAllProxies(ctx)
	if err != nil {
		m.logger.WithError(err).Error("Failed to get proxies for health check")
		return
	}

	if len(proxies) == 0 {
		return
	}

	// 使用并发健康检查器，限制goroutine数量
	_, err = m.BatchHealthCheck(ctx, proxies)
	if err != nil {
		m.logger.WithError(err).Error("Batch health check failed")
	}
}

// BatchHealthCheck 并发批量健康检查
func (m *Manager) BatchHealthCheck(ctx context.Context, proxies []*models.Proxy) ([]*models.ProxyHealthCheck, error) {
	if len(proxies) == 0 {
		return []*models.ProxyHealthCheck{}, nil
	}

	// 创建带超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, m.config.BatchTimeout)
	defer cancel()

	// 创建并发健康检查器
	checker := &ConcurrentHealthChecker{
		manager:     m,
		workerCount: m.config.ConcurrentWorkers,
		timeout:     m.config.Timeout,
		retryDelay:  m.config.RetryDelay,
		logger:      m.logger,
	}

	return checker.CheckProxies(timeoutCtx, proxies)
}

// CheckProxies 并发检查多个代理
func (c *ConcurrentHealthChecker) CheckProxies(ctx context.Context, proxies []*models.Proxy) ([]*models.ProxyHealthCheck, error) {
	// 创建任务通道和结果通道
	jobChan := make(chan HealthCheckJob, len(proxies))
	resultChan := make(chan HealthCheckResult, len(proxies))

	// 启动工作者
	var wg sync.WaitGroup
	workerCount := c.workerCount
	if workerCount > len(proxies) {
		workerCount = len(proxies) // 不需要比代理数量更多的工作者
	}

	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go c.worker(ctx, &wg, jobChan, resultChan)
	}

	// 发送任务
	go func() {
		defer close(jobChan)
		for _, proxy := range proxies {
			select {
			case jobChan <- HealthCheckJob{
				Proxy:     proxy,
				RetryLeft: c.manager.config.MaxRetries,
			}:
			case <-ctx.Done():
				return
			}
		}
	}()

	// 收集结果
	results := make([]*models.ProxyHealthCheck, 0, len(proxies))
	var resultWg sync.WaitGroup
	resultWg.Add(1)

	go func() {
		defer resultWg.Done()
		for range proxies {
			select {
			case result := <-resultChan:
				if result.Check != nil {
					results = append(results, result.Check)
				} else if result.Error != nil {
					c.logger.WithError(result.Error).Error("Health check failed")
				}
			case <-ctx.Done():
				c.logger.Warn("Health check timeout, some results may be missing")
				return
			}
		}
	}()

	wg.Wait()
	close(resultChan)

	resultWg.Wait()

	c.logger.WithFields(logrus.Fields{
		"total_proxies": len(proxies),
		"results":       len(results),
		"workers":       workerCount,
	}).Info("Concurrent health check completed")

	return results, nil
}

func (c *ConcurrentHealthChecker) worker(ctx context.Context, wg *sync.WaitGroup, jobChan <-chan HealthCheckJob, resultChan chan<- HealthCheckResult) {
	defer wg.Done()

	for {
		select {
		case job, ok := <-jobChan:
			if !ok {
				return // 通道已关闭
			}
			c.processJob(ctx, job, resultChan)
		case <-ctx.Done():
			return // 上下文取消
		}
	}
}

func (c *ConcurrentHealthChecker) processJob(ctx context.Context, job HealthCheckJob, resultChan chan<- HealthCheckResult) {
	start := time.Now()

	check := &models.ProxyHealthCheck{
		ProxyID:   job.Proxy.ID,
		CheckedAt: time.Now(),
	}

	// 执行健康检查（内置重试机制）
	err := c.manager.testProxyConnectionWithRetry(job.Proxy, job.RetryLeft)
	if err != nil {
		check.Status = models.ProxyStatusFailed
		check.Error = err.Error()
		job.Proxy.Failures++

		if job.Proxy.Failures >= c.manager.config.MaxFailures {
			job.Proxy.Status = models.ProxyStatusFailed
		}
	} else {
		check.Status = models.ProxyStatusActive
		job.Proxy.Status = models.ProxyStatusActive
		job.Proxy.Failures = 0
		now := time.Now()
		job.Proxy.LastSuccess = &now
	}

	check.ResponseTime = time.Since(start).Milliseconds()
	job.Proxy.ResponseTime = &check.ResponseTime
	now := time.Now()
	job.Proxy.LastCheck = &now
	job.Proxy.UpdatedAt = now

	// 异步更新代理状态，不阻塞健康检查
	go func() {
		if err := c.manager.updateProxy(ctx, job.Proxy); err != nil {
			c.logger.WithError(err).WithField("proxy_id", job.Proxy.ID).Error("Failed to update proxy status processJob")
		}
	}()

	// 发送结果
	select {
	case resultChan <- HealthCheckResult{Check: check}:
	case <-ctx.Done():
	}
}

func (m *Manager) testProxyConnectionWithRetry(proxy *models.Proxy, retryLeft int) error {
	err := m.testProxyConnection(proxy)
	if err != nil && retryLeft > 0 {
		time.Sleep(m.config.RetryDelay)
		return m.testProxyConnectionWithRetry(proxy, retryLeft-1)
	}
	return err
}

func (m *Manager) AssessProxyQuality(ctx context.Context, proxyID string) (*models.QualityScore, error) {
	proxy, err := m.GetProxyByID(ctx, proxyID) // 从数据库获取代理信息
	if err != nil {
		return nil, fmt.Errorf("failed to get proxy: %w", err)
	}

	if proxy == nil {
		return nil, fmt.Errorf("proxy not found")
	}

	score, err := m.qualityAssessment.AssessProxyQuality(ctx, proxy)
	if err != nil {
		return nil, fmt.Errorf("failed to assess quality: %w", err)
	}

	if err := m.proxyRepo.UpdateQualityScore(ctx, proxyID, *score); err != nil {
		m.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to update quality score")
		// 不返回错误，因为评估已经完成
	}

	return score, nil
}

func (m *Manager) BatchAssessQuality(ctx context.Context, proxyIDs []string) error {
	if len(proxyIDs) == 0 {
		return nil
	}

	proxies := make([]*models.Proxy, 0, len(proxyIDs))
	for _, id := range proxyIDs {
		proxy, err := m.GetProxyByID(ctx, id) // 从数据库获取代理信息
		if err != nil {
			m.logger.WithError(err).WithField("proxy_id", id).Error("Failed to get proxy for quality assessment")
			continue
		}
		if proxy != nil {
			proxies = append(proxies, proxy)
		}
	}

	if len(proxies) == 0 {
		return fmt.Errorf("no valid proxies found for assessment")
	}

	// 批量评估
	scores, err := m.qualityAssessment.BatchAssessQuality(ctx, proxies)
	if err != nil {
		return fmt.Errorf("failed to batch assess quality: %w", err)
	}

	// 批量更新数据库
	for i, proxy := range proxies {
		if i < len(scores) && scores[i] != nil {
			if err := m.proxyRepo.UpdateQualityScore(ctx, proxy.ID, *scores[i]); err != nil {
				m.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to update quality score")
			}
		}
	}

	return nil
}

// StartQualityAssessment 启动定期质量评估
func (m *Manager) StartQualityAssessment(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Hour) // 每小时评估一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			m.runQualityAssessment(ctx)
		}
	}
}

// runQualityAssessment 运行质量评估
func (m *Manager) runQualityAssessment(ctx context.Context) {
	m.logger.Info("Starting scheduled quality assessment")

	// 获取所有活跃代理
	proxies, err := m.GetAllProxies(ctx)
	if err != nil {
		m.logger.WithError(err).Error("Failed to get proxies for quality assessment")
		return
	}

	// 过滤出需要评估的代理（活跃状态且超过一定时间未评估）
	var needAssessment []*models.Proxy
	now := time.Now()
	for _, proxy := range proxies {
		if proxy.Status == models.ProxyStatusActive {
			// 如果从未评估过，或者距离上次评估超过6小时
			if proxy.LastQualityCheck == nil || now.Sub(*proxy.LastQualityCheck) > 6*time.Hour {
				needAssessment = append(needAssessment, proxy)
			}
		}
	}

	if len(needAssessment) == 0 {
		m.logger.Info("No proxies need quality assessment")
		return
	}

	m.logger.WithField("count", len(needAssessment)).Info("Starting quality assessment for proxies")

	// 批量评估
	scores, err := m.qualityAssessment.BatchAssessQuality(ctx, needAssessment)
	if err != nil {
		m.logger.WithError(err).Error("Failed to batch assess quality")
		return
	}

	// 更新数据库
	successCount := 0
	for i, proxy := range needAssessment {
		if i < len(scores) && scores[i] != nil {
			if err := m.proxyRepo.UpdateQualityScore(ctx, proxy.ID, *scores[i]); err != nil {
				m.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to update quality score")
			} else {
				successCount++
			}
		}
	}

	m.logger.WithFields(logrus.Fields{
		"total":   len(needAssessment),
		"success": successCount,
	}).Info("Quality assessment completed")
}

// GetProxiesByQualityRange 根据质量评分范围获取代理
func (m *Manager) GetProxiesByQualityRange(ctx context.Context, minScore, maxScore float64) ([]*models.Proxy, error) {
	return m.proxyRepo.GetByQualityRange(ctx, minScore, maxScore)
}

// GetTopQualityProxies 获取质量最高的代理
func (m *Manager) GetTopQualityProxies(ctx context.Context, limit int) ([]*models.Proxy, error) {
	return m.proxyRepo.GetTopQualityProxies(ctx, limit)
}

// GetProxyByTags 根据标签获取代理
func (m *Manager) GetProxyByTags(ctx context.Context, tagNames []string) ([]*models.Proxy, error) {
	return m.proxyRepo.GetByTags(ctx, tagNames)
}

// GetProxyByScenario 根据场景获取代理
func (m *Manager) GetProxyByScenario(ctx context.Context, scenario string) ([]*models.Proxy, error) {
	return m.proxyRepo.GetByScenario(ctx, scenario)
}

// GetProxyWithSmartRouting 智能路由获取代理
func (m *Manager) GetProxyWithSmartRouting(ctx context.Context, filter *models.ProxyFilterByTags) (*models.Proxy, error) {
	var candidates []*models.Proxy
	var err error

	// 根据筛选条件获取候选代理
	if len(filter.Tags) > 0 {
		// 按标签筛选
		candidates, err = m.proxyRepo.GetByTags(ctx, filter.Tags)
	} else if filter.Scenario != "" {
		// 按场景筛选
		candidates, err = m.proxyRepo.GetByScenario(ctx, filter.Scenario)
	} else {
		// 获取所有活跃代理
		allProxies, err := m.GetAllProxies(ctx)
		if err != nil {
			return nil, err
		}
		// 过滤出活跃代理
		for _, proxy := range allProxies {
			if proxy.Status == models.ProxyStatusActive {
				candidates = append(candidates, proxy)
			}
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get candidate proxies: %w", err)
	}

	if len(candidates) == 0 {
		return nil, fmt.Errorf("no suitable proxies found")
	}

	// 根据策略选择代理
	strategy := filter.Strategy
	if strategy == "" {
		strategy = "quality" // 默认使用质量优先策略
	}

	return m.selectProxyByStrategy(ctx, candidates, strategy)
}

// selectProxyByStrategy 根据策略选择代理
func (m *Manager) selectProxyByStrategy(ctx context.Context, candidates []*models.Proxy, strategy string) (*models.Proxy, error) {
	if len(candidates) == 0 {
		return nil, fmt.Errorf("no candidate proxies")
	}

	switch strategy {
	case "quality":
		// 质量优先：选择质量评分最高的代理
		return m.selectByQuality(candidates), nil
	case "speed":
		// 速度优先：选择响应时间最快的代理
		return m.selectBySpeed(candidates), nil
	case "stability":
		// 稳定性优先：选择稳定性最高的代理
		return m.selectByStability(candidates), nil
	case "priority":
		// 优先级优先：选择优先级最高的代理
		return m.selectByPriority(candidates), nil
	case "round_robin":
		// 轮询：按顺序选择
		return m.selectByRoundRobin(candidates), nil
	case "random":
		// 随机选择
		return m.selectByRandom(candidates), nil
	case "least_used":
		// 最少使用：选择使用次数最少的代理
		return m.selectByLeastUsed(candidates), nil
	default:
		// 默认使用质量优先
		return m.selectByQuality(candidates), nil
	}
}

// selectByQuality 按质量评分选择代理
func (m *Manager) selectByQuality(candidates []*models.Proxy) *models.Proxy {
	best := candidates[0]
	for _, proxy := range candidates[1:] {
		if proxy.QualityScore > best.QualityScore {
			best = proxy
		}
	}
	return best
}

// selectBySpeed 按速度选择代理
func (m *Manager) selectBySpeed(candidates []*models.Proxy) *models.Proxy {
	best := candidates[0]
	for _, proxy := range candidates[1:] {
		// 响应时间越小越好
		if proxy.ResponseTime != nil && best.ResponseTime != nil {
			if *proxy.ResponseTime < *best.ResponseTime {
				best = proxy
			}
		} else if proxy.ResponseTime != nil && best.ResponseTime == nil {
			best = proxy
		}
	}
	return best
}

// selectByStability 按稳定性选择代理
func (m *Manager) selectByStability(candidates []*models.Proxy) *models.Proxy {
	best := candidates[0]
	for _, proxy := range candidates[1:] {
		if proxy.StabilityScore > best.StabilityScore {
			best = proxy
		}
	}
	return best
}

// selectByPriority 按优先级选择代理
func (m *Manager) selectByPriority(candidates []*models.Proxy) *models.Proxy {
	best := candidates[0]
	for _, proxy := range candidates[1:] {
		if proxy.Priority > best.Priority {
			best = proxy
		}
	}
	return best
}

// selectByRoundRobin 轮询选择代理
func (m *Manager) selectByRoundRobin(candidates []*models.Proxy) *models.Proxy {
	index := atomic.AddInt64(&m.roundRobinIndex, 1) - 1
	return candidates[index%int64(len(candidates))]
}

// selectByRandom 随机选择代理
func (m *Manager) selectByRandom(candidates []*models.Proxy) *models.Proxy {
	index := rand.Intn(len(candidates))
	return candidates[index]
}

// selectByLeastUsed 选择使用次数最少的代理
func (m *Manager) selectByLeastUsed(candidates []*models.Proxy) *models.Proxy {
	best := candidates[0]
	for _, proxy := range candidates[1:] {
		if proxy.UseCount < best.UseCount {
			best = proxy
		}
	}
	return best
}

// 标签管理相关方法

// CreateProxyTag 创建代理标签
func (m *Manager) CreateProxyTag(ctx context.Context, req *models.ProxyTagRequest, createdBy string) (*models.ProxyTag, error) {
	// 检查标签名是否已存在
	existing, err := m.proxyTagRepo.GetByName(ctx, req.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing tag: %w", err)
	}
	if existing != nil {
		return nil, fmt.Errorf("tag with name '%s' already exists", req.Name)
	}

	tag := &models.ProxyTag{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		CreatedBy:   &createdBy,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 设置默认颜色
	if tag.Color == "" {
		tag.Color = "#3B82F6"
	}

	err = m.proxyTagRepo.Create(ctx, tag)
	if err != nil {
		return nil, fmt.Errorf("failed to create tag: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"tag_id":     tag.ID,
		"tag_name":   tag.Name,
		"created_by": createdBy,
	}).Info("Proxy tag created")

	return tag, nil
}

// GetProxyTags 获取所有代理标签
func (m *Manager) GetProxyTags(ctx context.Context) ([]*models.ProxyTag, error) {
	return m.proxyTagRepo.List(ctx)
}

// GetProxyTag 获取单个代理标签
func (m *Manager) GetProxyTag(ctx context.Context, id string) (*models.ProxyTag, error) {
	return m.proxyTagRepo.GetByID(ctx, id)
}

// UpdateProxyTag 更新代理标签
func (m *Manager) UpdateProxyTag(ctx context.Context, id string, req *models.ProxyTagRequest) (*models.ProxyTag, error) {
	tag, err := m.proxyTagRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}
	if tag == nil {
		return nil, fmt.Errorf("tag not found")
	}

	// 如果名称发生变化，检查新名称是否已存在
	if req.Name != tag.Name {
		existing, err := m.proxyTagRepo.GetByName(ctx, req.Name)
		if err != nil {
			return nil, fmt.Errorf("failed to check existing tag: %w", err)
		}
		if existing != nil && existing.ID != id {
			return nil, fmt.Errorf("tag with name '%s' already exists", req.Name)
		}
	}

	tag.Name = req.Name
	tag.Description = req.Description
	if req.Color != "" {
		tag.Color = req.Color
	}

	err = m.proxyTagRepo.Update(ctx, tag)
	if err != nil {
		return nil, fmt.Errorf("failed to update tag: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"tag_id":   tag.ID,
		"tag_name": tag.Name,
	}).Info("Proxy tag updated")

	return tag, nil
}

// DeleteProxyTag 删除代理标签
func (m *Manager) DeleteProxyTag(ctx context.Context, id string) error {
	tag, err := m.proxyTagRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get tag: %w", err)
	}
	if tag == nil {
		return fmt.Errorf("tag not found")
	}

	err = m.proxyTagRepo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete tag: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"tag_id":   tag.ID,
		"tag_name": tag.Name,
	}).Info("Proxy tag deleted")

	return nil
}

// AssignTagsToProxy 为代理分配标签
func (m *Manager) AssignTagsToProxy(ctx context.Context, proxyID string, tagIDs []string, assignedBy string) error {
	// 验证代理是否存在
	proxy, err := m.getProxyByID(ctx, proxyID)
	if err != nil {
		return fmt.Errorf("failed to get proxy: %w", err)
	}
	if proxy == nil {
		return fmt.Errorf("proxy not found")
	}

	// 验证标签是否存在
	for _, tagID := range tagIDs {
		tag, err := m.proxyTagRepo.GetByID(ctx, tagID)
		if err != nil {
			return fmt.Errorf("failed to get tag %s: %w", tagID, err)
		}
		if tag == nil {
			return fmt.Errorf("tag %s not found", tagID)
		}
	}

	err = m.proxyRepo.AssignTags(ctx, proxyID, tagIDs, assignedBy)
	if err != nil {
		return fmt.Errorf("failed to assign tags: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"proxy_id":    proxyID,
		"tag_ids":     tagIDs,
		"assigned_by": assignedBy,
	}).Info("Tags assigned to proxy")

	return nil
}

// RemoveTagsFromProxy 移除代理的标签
func (m *Manager) RemoveTagsFromProxy(ctx context.Context, proxyID string, tagIDs []string) error {
	err := m.proxyRepo.RemoveTags(ctx, proxyID, tagIDs)
	if err != nil {
		return fmt.Errorf("failed to remove tags: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"proxy_id": proxyID,
		"tag_ids":  tagIDs,
	}).Info("Tags removed from proxy")

	return nil
}

// GetProxyTagsByProxyID 获取代理的标签
func (m *Manager) GetProxyTagsByProxyID(ctx context.Context, proxyID string) ([]*models.ProxyTag, error) {
	return m.proxyRepo.GetProxyTags(ctx, proxyID)
}
