package app

import (
	"context"
	"fmt"
	"net/http"
	"path/filepath"
	"runtime"
	"time"

	"proxyFlow/api/handlers"
	"proxyFlow/api/routes"
	"proxyFlow/internal/auth"
	"proxyFlow/internal/collector"
	"proxyFlow/internal/middleware"
	"proxyFlow/internal/proxy"
	"proxyFlow/internal/repository"
	"proxyFlow/internal/repository/postgres"
	"proxyFlow/internal/task"
	"proxyFlow/pkg/config"
	"proxyFlow/pkg/database"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// Application 应用程序结构
type Application struct {
	config *config.Config
	logger *logrus.Logger
	server *http.Server

	// 数据库连接
	postgres *database.PostgresClient

	// 核心组件
	repoManager    repository.RepositoryManager
	proxyManager   *proxy.Manager
	taskManager    *task.Manager
	proxyCollector *collector.ProxyCollector

	// 取消函数
	cancelFuncs []context.CancelFunc
}

// NewApplication 创建新的应用程序实例
func NewApplication(cfg *config.Config, logger *logrus.Logger) (*Application, error) {
	app := &Application{
		config:      cfg,
		logger:      logger,
		cancelFuncs: make([]context.CancelFunc, 0),
	}

	if err := app.initializeDatabases(); err != nil {
		return nil, fmt.Errorf("failed to initialize databases: %w", err)
	}

	if err := app.runMigrations(); err != nil {
		return nil, fmt.Errorf("failed to run migrations: %w", err)
	}

	if err := app.initializeComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize components: %w", err)
	}

	if err := app.initializeAdminUser(); err != nil {
		return nil, fmt.Errorf("failed to initialize admin user: %w", err)
	}

	if err := app.setupHTTPServer(); err != nil {
		return nil, fmt.Errorf("failed to setup HTTP server: %w", err)
	}

	return app, nil
}

// Start 启动应用程序
func (app *Application) Start(ctx context.Context) error {
	app.logger.Info("Starting application components...")

	// 启动后台服务
	app.startBackgroundServices()

	// 启动HTTP服务器
	go func() {
		if err := app.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			app.logger.Fatalf("Failed to start server: %v", err)
		}
	}()

	app.logger.Infof("Server started on port %d, visit http://localhost:%d",
		app.config.Server.Port, app.config.Server.Port)

	return nil
}

// Stop 停止应用程序
func (app *Application) Stop(ctx context.Context) error {
	app.logger.Info("Stopping application...")

	// 停止HTTP服务器
	if err := app.server.Shutdown(ctx); err != nil {
		app.logger.Errorf("Server forced to shutdown: %v", err)
	}

	// 停止后台服务
	app.stopBackgroundServices()

	// 关闭数据库连接
	app.closeDatabases()

	return nil
}

func (app *Application) initializeDatabases() error {
	postgres, err := database.NewPostgresClient(&database.PostgresConfig{
		Host:            app.config.Postgres.Host,
		Port:            app.config.Postgres.Port,
		User:            app.config.Postgres.User,
		Password:        app.config.Postgres.Password,
		Database:        app.config.Postgres.Database,
		SSLMode:         app.config.Postgres.SSLMode,
		MaxOpenConns:    app.config.Postgres.MaxOpenConns,
		MaxIdleConns:    app.config.Postgres.MaxIdleConns,
		ConnMaxLifetime: app.config.Postgres.ConnMaxLifetime,
		ConnMaxIdleTime: app.config.Postgres.ConnMaxIdleTime,
	}, app.logger)
	if err != nil {
		return fmt.Errorf("failed to connect to PostgreSQL: %w", err)
	}
	app.postgres = postgres
	app.logger.Info("Connected to PostgreSQL")

	return nil
}

func (app *Application) runMigrations() error {
	app.logger.Info("Running database migrations...")

	migrationManager := database.NewMigrationManager(app.postgres.GetDB(), app.logger)
	migrationsDir := filepath.Join(app.getCurrentFileDir(), "..", "..", "migrations")

	if err := migrationManager.RunMigrations(context.Background(), migrationsDir); err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	app.logger.Info("Database migrations completed successfully")
	return nil
}

func (app *Application) initializeComponents() error {
	// 创建仓储管理器
	app.repoManager = postgres.NewRepositoryManager(app.postgres, app.logger)

	// 创建代理管理器
	proxyConfig := &proxy.Config{
		HealthCheckInterval: app.config.Proxy.HealthCheckInterval,
		MaxFailures:         app.config.Proxy.MaxFailures,
		Timeout:             app.config.Proxy.Timeout,
		MaxRetries:          app.config.Proxy.MaxRetries,
		ConcurrentWorkers:   100, // 适当增加并发工作者数量(检测代理并发)
		BatchTimeout:        60 * time.Second,
		RetryDelay:          2 * time.Second,
	}
	app.proxyManager = proxy.NewManager(app.repoManager.GetProxy(), app.repoManager.GetProxyTag(), proxyConfig, app.logger)

	// 创建任务管理器
	taskConfig := &task.Config{
		MaxConcurrent: app.config.Task.MaxConcurrent,
		QueueSize:     app.config.Task.QueueSize,
		RetryAttempts: app.config.Task.RetryAttempts,
		RetryDelay:    app.config.Task.RetryDelay,
	}
	app.taskManager = task.NewManager(app.proxyManager, taskConfig, app.logger)

	// 创建代理采集器
	app.proxyCollector = collector.NewProxyCollector(
		&app.config.Collector,
		app.repoManager.GetProxy(),
		app.logger,
	)

	return nil
}

func (app *Application) initializeAdminUser() error {
	app.logger.Info("Initializing admin user...")

	if err := initializeAdminUser(context.Background(), app.repoManager, app.config, app.logger); err != nil {
		return fmt.Errorf("failed to initialize admin user: %w", err)
	}

	app.logger.Info("Admin user initialization completed")
	return nil
}

func (app *Application) setupHTTPServer() error {
	// 创建JWT管理器
	jwtManager := auth.NewJWTManager(app.config.Auth.JWTSecret, app.config.Auth.TokenExpiry)

	// 创建认证中间件
	authMiddleware := middleware.NewAuthMiddleware(jwtManager, app.config.Auth.SuperApiKey,
		app.repoManager)

	// 创建处理器
	authHandler := handlers.NewAuthHandler(app.repoManager, jwtManager, app.logger)
	proxyHandler := handlers.NewProxyHandler(app.proxyManager, app.logger)
	taskHandler := handlers.NewTaskHandler(app.taskManager, app.logger)
	apiKeyHandler := handlers.NewAPIKeyHandler(app.repoManager, app.logger)
	settingsHandler := handlers.NewSettingsHandler(app.repoManager, app.logger)
	collectorHandler := handlers.NewCollectorHandler(app.proxyCollector, app.logger)

	// 设置Gin模式
	gin.SetMode(app.config.Server.Mode)

	// 创建路由
	router := gin.New()
	router.Use(middleware.CORS())
	router.Use(middleware.Logger(app.logger))
	router.Use(gin.Recovery())

	// 设置路由
	routes.SetupRoutes(router, authHandler, proxyHandler, taskHandler, apiKeyHandler,
		settingsHandler, collectorHandler, authMiddleware, app.proxyManager, app.logger)

	// 创建HTTP服务器
	app.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", app.config.Server.Port),
		Handler:      router,
		ReadTimeout:  app.config.Server.ReadTimeout,
		WriteTimeout: app.config.Server.WriteTimeout,
	}

	return nil
}

func (app *Application) startBackgroundServices() {
	// 启动健康检查
	healthCtx, healthCancel := context.WithCancel(context.Background())
	app.cancelFuncs = append(app.cancelFuncs, healthCancel)
	go app.proxyManager.StartHealthCheck(healthCtx)
	app.logger.Info("Started proxy health check")

	// 启动任务工作器
	workerCtx, workerCancel := context.WithCancel(context.Background())
	app.cancelFuncs = append(app.cancelFuncs, workerCancel)
	go app.taskManager.StartWorker(workerCtx)
	app.logger.Info("Started task worker")

	// 启动质量评估服务
	qualityCtx, qualityCancel := context.WithCancel(context.Background())
	app.cancelFuncs = append(app.cancelFuncs, qualityCancel)
	go app.proxyManager.StartQualityAssessment(qualityCtx)
	app.logger.Info("Started proxy quality assessment")

	// 代理采集器现在可以独立运行，不在主应用中自动启动
	// 使用 cmd/collector/main.go 或 API 端点来控制采集器
	app.logger.Info("Proxy collector available for standalone execution")
}

func (app *Application) stopBackgroundServices() {
	// 调用所有取消函数
	for _, cancel := range app.cancelFuncs {
		cancel()
	}

	// 停止采集器
	if err := app.proxyCollector.Stop(); err != nil {
		app.logger.Errorf("Failed to stop proxy collector: %v", err)
	}
}

func (app *Application) closeDatabases() {
	if app.postgres != nil {
		if err := app.postgres.Close(); err != nil {
			app.logger.Errorf("Failed to close PostgreSQL connection: %v", err)
		}
	}

}

func (app *Application) getCurrentFileDir() string {
	_, file, _, ok := runtime.Caller(1)
	if !ok {
		panic("unable to get current file path")
	}
	return filepath.Dir(file)
}
