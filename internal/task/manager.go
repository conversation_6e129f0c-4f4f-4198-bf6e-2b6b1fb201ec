package task

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"

	"proxyFlow/internal/models"
	"proxyFlow/internal/proxy"

	"github.com/sirupsen/logrus"
)

// TaskConfig 任务配置结构（用于存储在数据库的 config 字段中）
type TaskConfig struct {
	URL           string               `json:"url"`
	Method        string               `json:"method"`
	Headers       map[string]string    `json:"headers,omitempty"`
	Body          string               `json:"body,omitempty"`
	ProxyStrategy models.ProxyStrategy `json:"proxy_strategy"`
	Priority      models.TaskPriority  `json:"priority"`
	MaxRetries    int                  `json:"max_retries"`
	Timeout       int                  `json:"timeout"`
}

// TaskResult 任务结果结构（用于存储在数据库的 result 字段中）
type TaskResult struct {
	StatusCode      int               `json:"status_code"`
	ResponseBody    string            `json:"response_body"`
	ResponseHeaders map[string]string `json:"response_headers,omitempty"`
	ProxyID         string            `json:"proxy_id"`
	Duration        int64             `json:"duration"`
	Error           string            `json:"error,omitempty"`
}

// CreateTaskConfigFromRequest 从请求创建任务配置
func CreateTaskConfigFromRequest(req *models.TaskRequest) (*TaskConfig, error) {
	return &TaskConfig{
		URL:           req.URL,
		Method:        req.Method,
		Headers:       req.Headers,
		Body:          req.Body,
		ProxyStrategy: req.ProxyStrategy,
		Priority:      req.Priority,
		MaxRetries:    req.MaxRetries,
		Timeout:       req.Timeout,
	}, nil
}

// Manager 任务管理器
type Manager struct {
	proxyManager *proxy.Manager
	logger       *logrus.Logger
	config       *Config
	workerPool   chan struct{}
	// 内存任务队列
	taskQueue chan *models.Task
	tasks     map[string]*models.Task
	tasksMu   sync.RWMutex
}

// Config 任务管理器配置
type Config struct {
	MaxConcurrent int
	QueueSize     int
	RetryAttempts int
	RetryDelay    time.Duration
}

// NewManager 创建任务管理器
func NewManager(proxyManager *proxy.Manager, config *Config, logger *logrus.Logger) *Manager {
	return &Manager{
		proxyManager: proxyManager,
		logger:       logger,
		config:       config,
		workerPool:   make(chan struct{}, config.MaxConcurrent),
		taskQueue:    make(chan *models.Task, config.QueueSize),
		tasks:        make(map[string]*models.Task),
	}
}

func (m *Manager) CreateTask(ctx context.Context, req *models.TaskRequest, userID string) (*models.Task, error) {
	config, err := CreateTaskConfigFromRequest(req)
	if err != nil {
		return nil, fmt.Errorf("failed to create task config: %w", err)
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = m.config.RetryAttempts
	}

	configJSON, err := json.Marshal(config)
	if err != nil {
		return nil, fmt.Errorf("marshal task config error: %w", err)
	}

	now := time.Now()
	task := &models.Task{
		ID:          generateID(),
		UserID:      userID,
		Name:        req.Name,
		Description: "", // TaskRequest 没有 Description 字段
		Type:        "http_request",
		Status:      "pending",
		Config:      string(configJSON),
		Progress:    0,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	// 保存任务到内存
	m.tasksMu.Lock()
	m.tasks[task.ID] = task
	m.tasksMu.Unlock()

	// 添加到任务队列
	select {
	case m.taskQueue <- task:
		// 任务成功添加到队列
	default:
		// 队列已满，返回错误
		m.tasksMu.Lock()
		delete(m.tasks, task.ID)
		m.tasksMu.Unlock()
		return nil, fmt.Errorf("task queue is full")
	}

	m.logger.WithFields(logrus.Fields{
		"task_id": task.ID,
		"name":    task.Name,
		"url":     config.URL,
		"user_id": task.UserID,
	}).Info("Task created")

	return task, nil
}

func (m *Manager) GetTask(ctx context.Context, taskID string) (*models.Task, error) {
	m.tasksMu.RLock()
	defer m.tasksMu.RUnlock()

	task, exists := m.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("task not found")
	}
	return task, nil
}

func (m *Manager) GetUserTasks(ctx context.Context, userID string) ([]*models.Task, error) {
	m.tasksMu.RLock()
	defer m.tasksMu.RUnlock()

	var tasks []*models.Task
	for _, task := range m.tasks {
		if task.UserID == userID {
			tasks = append(tasks, task)
		}
	}

	return tasks, nil
}

func (m *Manager) UpdateTask(ctx context.Context, taskID string, req *models.TaskRequest, userID string) (*models.Task, error) {
	task, err := m.GetTask(ctx, taskID)
	if err != nil {
		return nil, err
	}

	if task.UserID != userID {
		return nil, fmt.Errorf("unauthorized to update this task")
	}

	if task.Status == "running" {
		return nil, fmt.Errorf("cannot update running task")
	}

	// 更新任务配置
	config, err := CreateTaskConfigFromRequest(req)
	if err != nil {
		return nil, fmt.Errorf("failed to create task config: %w", err)
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = m.config.RetryAttempts
	}

	configJSON, err := json.Marshal(config)
	if err != nil {
		return nil, fmt.Errorf("marshal task config error: %w", err)
	}

	// 更新任务字段
	task.Name = req.Name
	task.Config = string(configJSON)
	task.UpdatedAt = time.Now()

	// 如果任务状态是失败或取消，重置为待处理
	if task.Status == "failed" || task.Status == "cancelled" {
		task.Status = "pending"
		task.Progress = 0
		task.ErrorMessage = ""
		task.Result = ""
		task.StartedAt = nil
		task.CompletedAt = nil
	}

	// 保存更新的任务到内存
	m.tasksMu.Lock()
	m.tasks[taskID] = task
	m.tasksMu.Unlock()

	m.logger.WithFields(logrus.Fields{
		"task_id": task.ID,
		"name":    task.Name,
		"user_id": task.UserID,
	}).Info("Task updated")

	return task, nil
}

func (m *Manager) DeleteTask(ctx context.Context, taskID string, userID string) error {
	task, err := m.GetTask(ctx, taskID)
	if err != nil {
		return err
	}

	if task.UserID != userID {
		return fmt.Errorf("unauthorized to delete this task")
	}

	if task.Status == "running" {
		return fmt.Errorf("cannot delete running task")
	}

	// 从内存中删除任务
	m.tasksMu.Lock()
	delete(m.tasks, taskID)
	m.tasksMu.Unlock()

	m.logger.WithFields(logrus.Fields{
		"task_id": task.ID,
		"name":    task.Name,
		"user_id": task.UserID,
	}).Info("Task deleted")

	return nil
}

func (m *Manager) CancelTask(ctx context.Context, taskID string, userID string) error {
	task, err := m.GetTask(ctx, taskID)
	if err != nil {
		return err
	}

	if task.UserID != userID {
		return fmt.Errorf("unauthorized to cancel this task")
	}

	if task.Status == "completed" || task.Status == "failed" {
		return fmt.Errorf("cannot cancel completed or failed task")
	}

	task.Status = "cancelled"
	task.UpdatedAt = time.Now()

	// 更新内存中的任务
	m.tasksMu.Lock()
	m.tasks[taskID] = task
	m.tasksMu.Unlock()
	return nil
}

func (m *Manager) BatchDeleteTasks(ctx context.Context, taskIDs []string, userID string) error {
	for _, taskID := range taskIDs {
		if err := m.DeleteTask(ctx, taskID, userID); err != nil {
			m.logger.WithError(err).WithField("task_id", taskID).Error("Failed to delete task in batch")
			// 继续删除其他任务，不因为一个失败而停止
		}
	}
	return nil
}

// BatchCancelTasks 批量取消任务
func (m *Manager) BatchCancelTasks(ctx context.Context, taskIDs []string, userID string) error {
	for _, taskID := range taskIDs {
		if err := m.CancelTask(ctx, taskID, userID); err != nil {
			m.logger.WithError(err).WithField("task_id", taskID).Error("Failed to cancel task in batch")
			// 继续取消其他任务，不因为一个失败而停止
		}
	}
	return nil
}

func (m *Manager) StartWorker(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			m.processNextTask(ctx)
		}
	}
}

// 私有方法
func (m *Manager) processNextTask(ctx context.Context) {
	// 从内存队列获取任务
	select {
	case task := <-m.taskQueue:
		m.workerPool <- struct{}{} // 获取工作槽
		go func(t *models.Task) {
			defer func() { <-m.workerPool }() // 释放工作槽
			m.executeTask(ctx, t.ID)
		}(task)
	default:
		time.Sleep(1 * time.Second)
	}
}

func (m *Manager) executeTask(ctx context.Context, taskID string) {
	task, err := m.GetTask(ctx, taskID)
	if err != nil {
		m.logger.WithError(err).WithField("task_id", taskID).Error("Failed to get task")
		return
	}

	if task.Status == "cancelled" {
		return
	}

	var config TaskConfig
	if err := json.Unmarshal([]byte(task.Config), &config); err != nil {
		m.handleTaskError(ctx, task, fmt.Errorf("failed to parse task config: %w", err))
		return
	}

	// 更新任务状态为运行中
	task.Status = "running"
	task.Progress = 25
	now := time.Now()
	task.StartedAt = &now
	task.UpdatedAt = now
	m.updateTask(ctx, task)

	proxyObj, err := m.proxyManager.GetProxy(ctx, proxy.Strategy(config.ProxyStrategy))
	if err != nil {
		m.handleTaskError(ctx, task, fmt.Errorf("failed to get proxy: %w", err))
		return
	}

	task.Progress = 50
	task.UpdatedAt = time.Now()
	m.updateTask(ctx, task)

	// 执行HTTP请求
	result, err := m.executeHTTPRequest(ctx, &config, proxyObj)
	if err != nil {
		m.handleTaskError(ctx, task, err)
		return
	}

	// 更新任务为完成
	task.Status = "completed"
	task.Progress = 100
	now = time.Now()
	task.CompletedAt = &now
	task.UpdatedAt = now

	// 保存结果
	resultJSON, _ := json.Marshal(result)
	task.Result = string(resultJSON)
	m.updateTask(ctx, task)

	m.logger.WithFields(logrus.Fields{
		"task_id":     task.ID,
		"status":      task.Status,
		"proxy_id":    proxyObj.ID,
		"duration":    result.Duration,
		"status_code": result.StatusCode,
	}).Info("Task completed")
}

func (m *Manager) executeHTTPRequest(ctx context.Context, config *TaskConfig, proxy *models.Proxy) (*TaskResult, error) {
	start := time.Now()

	// 创建代理URL
	proxyURL := fmt.Sprintf("%s://%s:%d", proxy.Type, proxy.Host, proxy.Port)
	proxyURLParsed, err := url.Parse(proxyURL)
	if err != nil {
		return nil, err
	}

	if proxy.Username != "" && proxy.Password != "" {
		proxyURLParsed.User = url.UserPassword(proxy.Username, proxy.Password)
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: time.Duration(config.Timeout) * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyURLParsed),
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, config.Method, config.URL, nil)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	for key, value := range config.Headers {
		req.Header.Set(key, value)
	}

	// 设置请求体
	if config.Body != "" {
		req.Body = io.NopCloser(io.Reader(io.NewSectionReader(nil, 0, 0)))
		// 这里需要根据实际需求处理请求体
	}

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 构建响应头
	responseHeaders := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			responseHeaders[key] = values[0]
		}
	}

	duration := time.Since(start).Milliseconds()

	return &TaskResult{
		StatusCode:      resp.StatusCode,
		ResponseBody:    string(body),
		ResponseHeaders: responseHeaders,
		ProxyID:         proxy.ID,
		Duration:        duration,
	}, nil
}

func (m *Manager) handleTaskError(ctx context.Context, task *models.Task, err error) {
	// 解析任务配置以获取重试信息
	var config TaskConfig
	if parseErr := json.Unmarshal([]byte(task.Config), &config); parseErr != nil {
		m.logger.WithError(parseErr).Error("Failed to parse task config for error handling")
		config.MaxRetries = 3 // 默认值
	}

	// 从结果中获取当前重试次数
	var result TaskResult
	retryCount := 0
	if task.Result != "" {
		if parseErr := json.Unmarshal([]byte(task.Result), &result); parseErr == nil {
			// 这里可以从结果中获取重试次数，但目前结构中没有，所以使用默认逻辑
		}
	}
	retryCount++ // 增加重试次数

	task.ErrorMessage = err.Error()
	task.UpdatedAt = time.Now()

	if retryCount >= config.MaxRetries {
		task.Status = "failed"
		now := time.Now()
		task.CompletedAt = &now
		m.updateTask(ctx, task)

		m.logger.WithFields(logrus.Fields{
			"task_id": task.ID,
			"error":   err.Error(),
			"retries": retryCount,
		}).Error("Task failed after max retries")
	} else {
		// 重新加入队列进行重试
		select {
		case m.taskQueue <- task:
			// 任务重新加入队列成功
		default:
			// 队列已满，记录错误
			m.logger.WithField("task_id", task.ID).Error("Failed to requeue task: queue is full")
		}

		m.logger.WithFields(logrus.Fields{
			"task_id": task.ID,
			"error":   err.Error(),
			"retry":   retryCount,
		}).Warn("Task retry scheduled")

		// 延迟重试
		time.Sleep(m.config.RetryDelay)
	}
}

func (m *Manager) updateTask(ctx context.Context, task *models.Task) error {
	// 更新内存中的任务
	m.tasksMu.Lock()
	m.tasks[task.ID] = task
	m.tasksMu.Unlock()
	return nil
}

func generateID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 10)
}
