# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a high-performance proxy management system built with Go (backend) and React/TypeScript (frontend). The system provides automated proxy collection, health monitoring, task management, and a comprehensive web dashboard with monitoring integration.

## Development Commands

### Quick Start
```bash
# Initialize project and start all services
make quick-start

# OR step by step:
make init                    # Initialize configuration and dependencies
./scripts/dev.sh            # Start PostgreSQL
go run cmd/main.go          # Start backend server
cd web && pnpm dev         # Start frontend development server
```

### Backend Development
```bash
make dev                    # Run backend with hot reload (using air if available)
go run cmd/main.go         # Run backend directly
make build                 # Build binary
make test                  # Run all tests
make test-coverage         # Run tests with coverage report
```

### Frontend Development
```bash
cd web
pnpm dev                   # Start development server (http://localhost:5173)
pnpm build                 # Build for production
pnpm lint                  # Run ESLint
pnpm type-check           # TypeScript type checking
```

### Database & Services
```bash
./scripts/dev.sh start     # Start PostgreSQL containers
./scripts/dev.sh stop      # Stop database services
make docker-run           # Start all services with Docker Compose
make docker-stop          # Stop Docker services
```

### Code Quality
```bash
make fmt                   # Format Go code
make lint                  # Run golangci-lint (if installed)
make security             # Run security scan with gosec
```

### Monitoring
```bash
make monitoring           # Start Prometheus + Grafana monitoring
make monitoring-test      # Test monitoring system
make health-check        # Check all service health
```

## Architecture

### Backend Structure
- **cmd/main.go**: Application entry point and service initialization
- **api/**: HTTP handlers and routing
  - **handlers/**: Request handlers for different domains (auth, proxy, task, etc.)
  - **routes/**: Route definitions and middleware setup
- **internal/**: Core business logic
  - **auth/**: JWT authentication and password hashing
  - **collector/**: Automated proxy collection from multiple sources
  - **middleware/**: HTTP middleware (auth, CORS, logging)
  - **models/**: Database models and domain entities
  - **proxy/**: Proxy management, health checking, and quality assessment
  - **repository/**: Data access layer with PostgreSQL implementation
  - **task/**: Asynchronous task management system
- **pkg/**: Shared packages
  - **config/**: Configuration management
  - **database/**: Database connection and migration handling

### Frontend Structure
- **src/components/**: Reusable UI components organized by domain
- **src/pages/**: Main application pages
- **src/services/**: API communication layer
- **src/contexts/**: React contexts for state management
- **src/hooks/**: Custom React hooks
- **src/types/**: TypeScript type definitions

### Key Components

#### Proxy Management
- Automated collection from multiple sources (internal/collector/)
- Health checking with configurable intervals and failure thresholds
- Quality scoring based on response time, reliability, and anonymity
- Load balancing strategies: round-robin, least-used, random, weighted

#### Task System
- Asynchronous task queue for proxy testing and batch operations
- Configurable concurrency and retry mechanisms
- WebSocket real-time updates for task progress

#### Authentication & Authorization
- JWT-based authentication with refresh tokens
- Role-based access control (RBAC) system
- API key authentication for programmatic access

#### Monitoring Integration
- Prometheus metrics for application
- Grafana dashboards for visualization
- Health check endpoints and service status monitoring

## Configuration

### Environment Setup
The system uses environment variables and YAML configuration:
- **.env**: Database credentials and secrets
- **config/config.yaml**: Application configuration
- Use `make init` to generate default configurations

### Key Environment Variables
- `PROXY_MANAGER_POSTGRES_PASSWORD`: PostgreSQL password
- `PROXY_MANAGER_ADMIN_USERNAME`: Admin user (default: admin)
- `PROXY_MANAGER_ADMIN_PASSWORD`: Admin password (default: admin123)

## Database

### PostgreSQL
- Primary database with automatic migrations in `migrations/` directory
- Connection pooling and performance optimization
- Full RBAC implementation with user management

## Testing

### Backend Tests
```bash
make test                  # Run all Go tests
make test-coverage        # Generate coverage report
go test ./internal/...    # Test specific packages
go test -v ./internal/collector/    # Test single package with verbose output
make bench                # Run performance benchmarks
```

### Frontend Tests
```bash
cd web
pnpm test                 # Run tests (when framework is added)
pnpm lint                 # Run ESLint
pnpm type-check          # TypeScript type checking
pnpm format              # Format code with Prettier
pnpm format:check        # Check code formatting
```

The frontend uses Vite with TypeScript. Testing framework is not currently configured but can be added as needed.

## Deployment

### Development
```bash
make deploy               # Deploy to development environment
make docker-deploy       # Deploy with Docker Compose
```

### Production
```bash
make deploy-prod         # Deploy to production
make docker-deploy-full  # Deploy with monitoring included
```

## Common Patterns

### Adding New API Endpoints
1. Create handler in `api/handlers/`
2. Add route in `api/routes/routes.go`
3. Implement repository interface if database access needed
4. Add authentication middleware if required

### Database Changes
1. Add new migration file in `migrations/` following naming convention
2. Update models in `internal/models/`
3. Implement repository methods for data access
4. Migration runs automatically on startup

### Frontend Development
- Use existing API service patterns in `src/services/`
- Follow component organization by domain
- Implement TypeScript interfaces in `src/types/`
- Use existing hooks for state management

## Monitoring & Observability

- **Metrics**: http://localhost:8080/metrics (Prometheus format)
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Health Check**: http://localhost:8080/health

The system includes comprehensive monitoring for proxy collection metrics, and application health.