# Proxy Manager

一个基于Golang的高性能代理服务系统。

## 功能特性

- 🚀 **高性能**: 基于Goroutine的高并发处理
- 🔐 **安全认证**: JWT认证和权限控制
- 🐳 **容器化**: Docker支持
- 📝 **API文档**: 完整的RESTful API
- 🔋 **内置电池**: 内置大量免费代理
- 🤖 **自动采集**: 智能采集和验证免费代理
- 🎯 **质量评估**: 多维度代理质量评分系统
- 🔄 **代理池管理**: 自动健康检查和动态更新
- ⚖️ **智能调度**: 多种负载均衡策略
- 📊 **实时监控**: Prometheus + Grafana监控

## 🚀 快速开始（简化版）

### 一键初始化（推荐）
```bash
# 1. 一键初始化项目
./scripts/init.sh

# 2. 启动数据库服务
./scripts/dev.sh

# 3. 启动后端应用
go run cmd/main.go

# 4. 启动前端应用
cd web && pnpm dev
```

就这么简单！访问 http://localhost:5173 开始使用。

### 代理采集器

系统内置智能代理采集器，自动从多个免费代理源采集和验证代理：

```bash
# 查看采集器状态
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/collector/status

# 手动触发采集
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  http://localhost:8080/api/v1/collector/collect
```

详细使用说明请参考 [代理采集器指南](docs/COLLECTOR_GUIDE.md)

### 环境要求

- Go 1.21+
- PostgreSQL 12+ (新增，用于数据持久化)
- Node.js 16+ (用于前端开发)
- Docker (可选)

## 许可证

MIT License
